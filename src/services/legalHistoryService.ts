import { processApiResponse } from '@/lib/authUtils';
import { apiClient } from '../lib/apiClient';

export interface LegalHistoryData {
  legal_history_id?: string;
  application_id?: string;
  criminal_history: boolean;
  criminal_details?: string;
  bankruptcy_history: boolean;
  bankruptcy_details?: string;
  regulatory_actions: boolean;
  regulatory_details?: string;
  litigation_history: boolean;
  litigation_details?: string;
  compliance_record?: string;
  previous_licenses?: string;
  declaration_accepted: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface CreateLegalHistoryData {
  application_id: string;
  criminal_history: boolean;
  criminal_details?: string;
  bankruptcy_history: boolean;
  bankruptcy_details?: string;
  regulatory_actions: boolean;
  regulatory_details?: string;
  litigation_history: boolean;
  litigation_details?: string;
  compliance_record?: string;
  previous_licenses?: string;
  declaration_accepted: boolean;
}

export interface UpdateLegalHistoryData {
  legal_history_id: string;
  criminal_history?: boolean;
  criminal_details?: string;
  bankruptcy_history?: boolean;
  bankruptcy_details?: string;
  regulatory_actions?: boolean;
  regulatory_details?: string;
  litigation_history?: boolean;
  litigation_details?: string;
  compliance_record?: string;
  previous_licenses?: string;
  declaration_accepted?: boolean;
}

class LegalHistoryService {
  private baseUrl = '/legal-history';

  // Create new legal history
  async createLegalHistory(data: CreateLegalHistoryData): Promise<LegalHistoryData> {
    try {
      console.log('🔧 Creating legal history record:', data);
      const response = await apiClient.post(this.baseUrl, data);
      return processApiResponse(response);
    } catch (error: any) {
      console.error('❌ Error creating legal history:', error);
      throw error;
    }
  }

  // Update legal history
  async updateLegalHistory(data: UpdateLegalHistoryData): Promise<LegalHistoryData> {
    try {
      console.log('🔧 Updating legal history record:', data.legal_history_id);
      const response = await apiClient.put(`${this.baseUrl}/${data.legal_history_id}`, data);
      return processApiResponse(response);
    } catch (error: any) {
      console.error('❌ Error updating legal history:', error);
      throw error;
    }
  }

  // Get legal history by application ID
  async getLegalHistoryByApplication(applicationId: string): Promise<LegalHistoryData | null> {
    try {
      console.log('🔧 Getting legal history for application:', applicationId);
      const response = await apiClient.get(`${this.baseUrl}/application/${applicationId}`);
      return processApiResponse(response);
    } catch (error: any) {
      if (error.response?.status === 404) {
        console.log('📝 No legal history found for application:', applicationId);
        return null;
      }
      console.error('❌ Error getting legal history:', error);
      throw error;
    }
  }

  // Get legal history by ID
  async getLegalHistory(legalHistoryId: string): Promise<LegalHistoryData> {
    try {
      console.log('🔧 Getting legal history:', legalHistoryId);
      const response = await apiClient.get(`${this.baseUrl}/${legalHistoryId}`);
      return processApiResponse(response);
    } catch (error: any) {
      console.error('❌ Error getting legal history:', error);
      throw error;
    }
  }

  // Delete legal history
  async deleteLegalHistory(legalHistoryId: string): Promise<void> {
    try {
      console.log('🔧 Deleting legal history:', legalHistoryId);
      const response = await apiClient.delete(`${this.baseUrl}/${legalHistoryId}`);
      return processApiResponse(response);
    } catch (error: any) {
      console.error('❌ Error deleting legal history:', error);
      throw error;
    }
  }

  // Get all legal history (admin only)
  async getAllLegalHistory(): Promise<LegalHistoryData[]> {
    try {
      console.log('🔧 Getting all legal history');
      const response = await apiClient.get(this.baseUrl);
      return processApiResponse(response);
    } catch (error: any) {
      console.error('❌ Error getting all legal history:', error);
      throw error;
    }
  }

  // Create or update legal history for an application
  async createOrUpdateLegalHistory(applicationId: string, data: Omit<CreateLegalHistoryData, 'application_id'>): Promise<LegalHistoryData> {
    try {
      // Use the backend's combined create/update endpoint
      const response = await apiClient.post(`${this.baseUrl}/application/${applicationId}`, data);
      return processApiResponse(response);
    } catch (error) {
      console.error('LegalHistoryService.createOrUpdateLegalHistory error:', error);
      throw error;
    }
  }
}

export const legalHistoryService = new LegalHistoryService();
