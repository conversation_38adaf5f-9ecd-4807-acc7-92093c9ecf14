import { apiClient } from '../lib/apiClient';
import { processApiResponse } from '../lib/authUtils';

// Types following backend entity structure
export interface Department {
  department_id: string;
  code: string;
  name: string;
  description: string;
  email: string;
  manager_id?: string;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
  created_by?: string;
  updated_by?: string;

  // Related data
  creator?: {
    user_id: string;
    first_name: string;
    last_name: string;
    email: string;
  };

  updater?: {
    user_id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
}

export interface CreateDepartmentDto {
  code: string;
  name: string;
  description: string;
  email: string;
  manager_id?: string;
  created_by?: string;
}

export interface UpdateDepartmentDto {
  code?: string;
  name?: string;
  description?: string;
  email?: string;
  manager_id?: string;
  updated_by?: string;
}

// Pagination interfaces following user service pattern
export interface PaginatedResponse<T> {
  data: T[];
  meta: {
    itemsPerPage: number;
    totalItems?: number;
    currentPage?: number;
    totalPages?: number;
    sortBy: [string, string][];
    searchBy: string[];
    search: string;
    select: string[];
    filter?: Record<string, string | string[]>;
  };
  links: {
    first?: string;
    previous?: string;
    current: string;
    next?: string;
    last?: string;
  };
}

export interface PaginateQuery {
  page?: number;
  limit?: number;
  sortBy?: string[];
  searchBy?: string[];
  search?: string;
  filter?: Record<string, string | string[]>;
}

export type DepartmentsResponse = PaginatedResponse<Department>;

export const departmentService = {
  // Create new department
  async createDepartment(data: CreateDepartmentDto): Promise<Department> {
    try {
      console.log('🔄 Creating department:', {
        code: data.code,
        name: data.name,
        email: data.email
      });

      const response = await apiClient.post('/department', data);
      return processApiResponse(response);
    } catch (error) {
      console.error('❌ Error creating department:', error);
      throw error;
    }
  },

  // Get departments with pagination (for management table)
  async getDepartments(query: PaginateQuery = {}): Promise<DepartmentsResponse> {
    try {
      const params = new URLSearchParams();

      if (query.page) params.set('page', query.page.toString());
      if (query.limit) params.set('limit', query.limit.toString());
      if (query.search) params.set('search', query.search);
      if (query.sortBy) {
        query.sortBy.forEach(sort => params.append('sortBy', sort));
      }
      if (query.searchBy) {
        query.searchBy.forEach(search => params.append('searchBy', search));
      }
      if (query.filter) {
        Object.entries(query.filter).forEach(([key, value]) => {
          if (Array.isArray(value)) {
            value.forEach(v => params.append(`filter.${key}`, v));
          } else {
            params.set(`filter.${key}`, value);
          }
        });
      }

      const response = await apiClient.get(`/department?${params.toString()}`);
      return processApiResponse(response);
    } catch (error) {
      console.error('Error fetching departments:', error);
      throw error;
    }
  },

  // Get all departments (simple list for dropdowns)
  async getAllDepartments(): Promise<Department[]> {
    try {
      const response = (await this.getDepartments()).data;
      return response;
    } catch (error) {
      console.error('Error fetching departments:', error);
      throw error;
    }
  },

  // Get department by ID
  async getDepartment(id: string): Promise<Department> {
    const response = await apiClient.get(`/department/${id}`);
    return processApiResponse(response);
  },

  // Get department by ID (alias for consistency)
  async getDepartmentById(id: string): Promise<Department> {
    return this.getDepartment(id);
  },

  // Update department
  async updateDepartment(id: string, data: UpdateDepartmentDto): Promise<Department> {
    const response = await apiClient.put(`/department/${id}`, data);
    return processApiResponse(response);
  },

  // Delete department (soft delete)
  async deleteDepartment(id: string): Promise<void> {
    await apiClient.delete(`/department/${id}`);
  },

  // Helper methods
  formatDepartmentCode(code: string): string {
    return code.toUpperCase();
  },

  formatDepartmentName(name: string): string {
    return name.charAt(0).toUpperCase() + name.slice(1);
  },

  validateDepartmentCode(code: string): boolean {
    return /^[A-Z]{1,5}$/.test(code);
  },

  validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },
};
