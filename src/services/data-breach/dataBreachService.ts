import { apiClient } from '@/lib/apiClient';
import { processApiResponse } from '@/lib/authUtils';

// Types following backend entity structure
export interface DataBreachReport {
  report_id: string;
  report_number: string;
  reporter_id: string;
  title: string;
  description: string;
  category: DataBreachCategory;
  severity: DataBreachSeverity;
  status: string;
  priority: DataBreachPriority;
  incident_date: string;
  organization_involved: string;
  affected_data_types?: string;
  contact_attempts?: string;
  assigned_to?: string;
  resolution?: string;
  internal_notes?: string;
  resolved_at?: string;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
  created_by?: string;
  updated_by?: string;

  // Related data
  reporter?: {
    user_id: string;
    first_name: string;
    last_name: string;
    email: string;
  };

  assignee?: {
    user_id: string;
    first_name: string;
    last_name: string;
    email: string;
  };

  attachments?: DataBreachReportAttachment[];
  status_history?: DataBreachReportStatusHistory[];
}

export interface DataBreachReportAttachment {
  attachment_id: string;
  report_id: string;
  file_name: string;
  file_type: string;
  file_size: number;
  file_path: string;
  uploaded_at: string;
  uploaded_by: string;
}

export interface DataBreachReportStatusHistory {
  history_id: string;
  report_id: string;
  status: DataBreachStatus;
  comment?: string;
  created_at: string;
  created_by: string;
}

// Enums matching backend
export enum DataBreachCategory {
  PERSONAL_DATA = 'Personal Data',
  FINANCIAL_DATA = 'Financial Data',
  HEALTH_DATA = 'Health Data',
  TECHNICAL_DATA = 'Technical Data',
  COMMUNICATION_DATA = 'Communication Data',
  OTHER = 'Other',
}

export enum DataBreachSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

export enum DataBreachStatus {
  SUBMITTED = 'submitted',
  UNDER_REVIEW = 'under_review',
  INVESTIGATING = 'investigating',
  RESOLVED = 'resolved',
  CLOSED = 'closed',
}

export enum DataBreachPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
}

export interface CreateDataBreachReportData {
  title: string;
  description: string;
  category: DataBreachCategory;
  severity: DataBreachSeverity;
  priority?: DataBreachPriority;
  incident_date: string;
  organization_involved: string;
  affected_data_types?: string;
  contact_attempts?: string;
  attachments?: File[];
}

export interface UpdateDataBreachReportData {
  title?: string;
  description?: string;
  category?: DataBreachCategory;
  severity?: DataBreachSeverity;
  status?: DataBreachStatus;
  priority?: DataBreachPriority;
  incident_date?: string;
  organization_involved?: string;
  affected_data_types?: string;
  contact_attempts?: string;
  assigned_to?: string;
  resolution?: string;
  internal_notes?: string;
  resolved_at?: string;
}

// Pagination interfaces following user service pattern
export interface PaginatedResponse<T> {
  data: T[];
  meta: {
    itemsPerPage: number;
    totalItems: number;
    currentPage: number;
    totalPages: number;
    sortBy: string[][];
    searchBy: string[];
    search: string;
    filter: Record<string, string | string[]>;
  };
  links: {
    first: string;
    previous: string;
    current: string;
    next: string;
    last: string;
  };
}

export interface PaginateQuery {
  page?: number;
  limit?: number;
  sortBy?: string[];
  searchBy?: string[];
  search?: string;
  filter?: Record<string, string | string[]>;
}

export type DataBreachReportsResponse = PaginatedResponse<DataBreachReport>;

export const dataBreachService = {
  // Create new report
  async createReport(data: CreateDataBreachReportData): Promise<DataBreachReport> {
    try {
      console.log('🔄 Creating data breach report:', {
        title: data.title,
        category: data.category,
        severity: data.severity,
        hasAttachments: data.attachments && data.attachments.length > 0
      });

      const formData = new FormData();
      formData.append('title', data.title);
      formData.append('description', data.description);
      formData.append('category', data.category);
      formData.append('severity', data.severity);
      formData.append('incident_date', data.incident_date);
      formData.append('organization_involved', data.organization_involved);

      if (data.priority) {
        formData.append('priority', data.priority);
      }

      if (data.affected_data_types) {
        formData.append('affected_data_types', data.affected_data_types);
      }

      if (data.contact_attempts) {
        formData.append('contact_attempts', data.contact_attempts);
      }

      // Add attachments if provided
      if (data.attachments && data.attachments.length > 0) {
        data.attachments.forEach((file) => {
          formData.append('attachments', file);
        });
      }

      const response = await apiClient.post('/data-breach-reports', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      console.log('✅ Data breach report created successfully:', response.data);
      return processApiResponse(response);
    } catch (error) {
      console.error('❌ Error creating data breach report:', error);
      throw error;
    }
  },

  // Get report by ID
  async getReportById(reportId: string): Promise<DataBreachReport> {
    try {
      console.log('🔄 Fetching data breach report by ID:', reportId);

      const response = await apiClient.get(`/data-breach-reports/${reportId}`);

      console.log('✅ Data breach report fetched successfully:', response.data);
      return processApiResponse(response);
    } catch (error) {
      console.error('❌ Error fetching data breach report:', error);
      throw error;
    }
  },

  // Update report status
  async updateStatus(reportId: string, status: string, comment?: string): Promise<DataBreachReport> {
    try {
      console.log('🔄 Updating data breach report status:', { reportId, status, comment });

      const response = await apiClient.put(`/data-breach-reports/${reportId}/status`, {
        status,
        comment
      });

      console.log('✅ Data breach report status updated successfully:', response.data);
      return processApiResponse(response);
    } catch (error) {
      console.error('❌ Error updating data breach report status:', error);
      throw error;
    }
  },

  // Assign report to officer
  async assignReport(reportId: string, assignedTo: string): Promise<DataBreachReport> {
    try {
      console.log('🔄 Assigning data breach report:', { reportId, assignedTo });

      const response = await apiClient.put(`/data-breach-reports/${reportId}/assign`, {
        assigned_to: assignedTo
      });

      console.log('✅ Data breach report assigned successfully:', response.data);
      return processApiResponse(response);
    } catch (error) {
      console.error('❌ Error assigning data breach report:', error);
      throw error;
    }
  },

  // Get all reports with pagination
  async getReports(query: PaginateQuery = {}): Promise<any> {
    const params = new URLSearchParams();

    if (query.page) params.set('page', query.page.toString());
    if (query.limit) params.set('limit', query.limit.toString());
    if (query.search) params.set('search', query.search);
    if (query.sortBy) {
      query.sortBy.forEach(sort => params.append('sortBy', sort));
    }
    if (query.searchBy) {
      query.searchBy.forEach(search => params.append('searchBy', search));
    }
    if (query.filter) {
      Object.entries(query.filter).forEach(([key, value]) => {
        if (Array.isArray(value)) {
          value.forEach(v => params.append(`filter.${key}`, v));
        } else {
          params.set(`filter.${key}`, value);
        }
      });
    }

    const response = await apiClient.get(`/data-breach-reports?${params.toString()}`);
    return processApiResponse(response);
  },

  // Get report by ID
  async getReport(id: string): Promise<DataBreachReport> {
    const response = await apiClient.get(`/data-breach-reports/${id}`);
    return processApiResponse(response);
  },

  // Update report
  async updateReport(id: string, data: UpdateDataBreachReportData): Promise<DataBreachReport> {
    const response = await apiClient.put(`/data-breach-reports/${id}`, data);
    return processApiResponse(response);
  },

  // Delete report
  async deleteReport(id: string): Promise<void> {
    await apiClient.delete(`/data-breach-reports/${id}`);
  },

  // Update report status (for staff)
  async updateReportStatus(id: string, status: DataBreachStatus, comment?: string): Promise<DataBreachReport> {
    const response = await apiClient.put(`/data-breach-reports/${id}/status`, {
      status,
      comment
    });
    return processApiResponse(response);
  },

  // Add attachment to report
  async addAttachment(id: string, file: File): Promise<DataBreachReportAttachment> {
    const formData = new FormData();
    formData.append('files', file);

    const response = await apiClient.post(`/data-breach-reports/${id}/attachments`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return processApiResponse(response);
  },

  // Remove attachment from report
  async removeAttachment(reportId: string, attachmentId: string): Promise<void> {
    await apiClient.delete(`/data-breach-reports/${reportId}/attachments/${attachmentId}`);
  },

  // Helper methods
  getStatusColor(status: string): string {
    switch (status?.toLowerCase()) {
      case 'submitted': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'under_review': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'investigating': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case 'resolved': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'closed': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  },

  getSeverityColor(severity: string): string {
    switch (severity?.toLowerCase()) {
      case 'low': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'high': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case 'critical': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  },

  getStatusOptions(): Array<{ value: string; label: string }> {
    return [
      { value: 'submitted', label: 'Submitted' },
      { value: 'under_review', label: 'Under Review' },
      { value: 'investigating', label: 'Investigating' },
      { value: 'resolved', label: 'Resolved' },
      { value: 'closed', label: 'Closed' }
    ];
  },

  getCategoryOptions(): Array<{ value: string; label: string }> {
    return [
      { value: 'Personal Data', label: 'Personal Data' },
      { value: 'Financial Data', label: 'Financial Data' },
      { value: 'Health Data', label: 'Health Data' },
      { value: 'Technical Data', label: 'Technical Data' },
      { value: 'Communication Data', label: 'Communication Data' },
      { value: 'Other', label: 'Other' }
    ];
  },

  getSeverityOptions(): Array<{ value: string; label: string }> {
    return [
      { value: 'low', label: 'Low' },
      { value: 'medium', label: 'Medium' },
      { value: 'high', label: 'High' },
      { value: 'critical', label: 'Critical' }
    ];
  },

  getPriorityOptions(): Array<{ value: string; label: string }> {
    return [
      { value: 'low', label: 'Low' },
      { value: 'medium', label: 'Medium' },
      { value: 'high', label: 'High' },
      { value: 'urgent', label: 'Urgent' }
    ];
  },
};


