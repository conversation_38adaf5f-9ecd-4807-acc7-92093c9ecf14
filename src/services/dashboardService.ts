import { apiClient } from '../lib/apiClient';
import { processApiResponse } from '@/lib/authUtils';

export interface DashboardOverview {
  applications: {
    total: number;
    pending: number;
    approved: number;
    rejected: number;
    submitted?: number;
    under_review?: number;
    evaluation?: number;
    draft?: number;
  };
  users?: {
    total: number;
    active: number;
    newThisMonth: number;
    administrators: number;
  };
  licenses: {
    total: number;
    active: number;
    expiringSoon: number;
    expired: number;
  };
  financial: {
    totalRevenue: number;
    thisMonth: number;
    pending: number;
    transactions: number;
  };
  timestamp: string;
}

export interface LicenseStats {
  total: number;
  active: number;
  expiringSoon: number;
  expired: number;
}

export interface UserStats {
  total: number;
  active: number;
  newThisMonth: number;
  administrators: number;
}

export interface FinancialStats {
  totalRevenue: number;
  thisMonth: number;
  pending: number;
  transactions: number;
}

export interface RecentApplication {
  application_id: string;
  application_number: string;
  status: string;
  created_at: string;
  applicant?: {
    company_name: string;
  };
  license_category?: {
    category_name: string;
  };
}

export interface RecentActivity {
  audit_id: string;
  action: string;
  module: string;
  resource_type: string;
  description: string;
  created_at: string;
  user?: {
    first_name: string;
    last_name: string;
  };
}

// Default fallback data
const getDefaultOverview = (): DashboardOverview => ({
  applications: {
    total: 0,
    pending: 0,
    approved: 0,
    rejected: 0,
    submitted: 0,
    under_review: 0,
    evaluation: 0,
    draft: 0,
  },
  users: {
    total: 0,
    active: 0,
    newThisMonth: 0,
    administrators: 0,
  },
  licenses: {
    total: 0,
    active: 0,
    expiringSoon: 0,
    expired: 0,
  },
  financial: {
    totalRevenue: 0,
    thisMonth: 0,
    pending: 0,
    transactions: 0,
  },
  timestamp: new Date().toISOString(),
});

export const dashboardService = {
  // Get dashboard overview with all key metrics
  async getOverview(): Promise<DashboardOverview> {
    try {
      const response = await apiClient.get('/dashboard/overview');
      const data = processApiResponse(response);

      // Ensure all required fields are present with fallbacks
      return {
        applications: data.applications || getDefaultOverview().applications,
        users: data.users || getDefaultOverview().users,
        licenses: data.licenses || getDefaultOverview().licenses,
        financial: data.financial || getDefaultOverview().financial,
        timestamp: data.timestamp || new Date().toISOString(),
      };
    } catch (error) {
      console.error('DashboardService.getOverview error:', error);
      // Return default data instead of throwing
      return getDefaultOverview();
    }
  },

  // Get license statistics
  async getLicenseStats(): Promise<LicenseStats> {
    try {
      const response = await apiClient.get('/dashboard/licenses/stats');
      const data = processApiResponse(response);
      return data.data || data;
    } catch (error) {
      console.error('DashboardService.getLicenseStats error:', error);
      return {
        total: 0,
        active: 0,
        expiringSoon: 0,
        expired: 0,
      };
    }
  },

  // Get user statistics (admin only)
  async getUserStats(): Promise<UserStats> {
    try {
      const response = await apiClient.get('/dashboard/users/stats');
      const data = processApiResponse(response);
      return data.data || data;
    } catch (error) {
      console.error('DashboardService.getUserStats error:', error);
      return {
        total: 0,
        active: 0,
        newThisMonth: 0,
        administrators: 0,
      };
    }
  },

  // Get financial statistics
  async getFinancialStats(): Promise<FinancialStats> {
    try {
      const response = await apiClient.get('/dashboard/financial/stats');
      const data = processApiResponse(response);
      return data.data || data;
    } catch (error) {
      console.error('DashboardService.getFinancialStats error:', error);
      return {
        totalRevenue: 0,
        thisMonth: 0,
        pending: 0,
        transactions: 0,
      };
    }
  },

  // Get recent applications
  async getRecentApplications(): Promise<RecentApplication[]> {
    try {
      const response = await apiClient.get('/dashboard/applications/recent');
      const data = processApiResponse(response);
      return data.data || data || [];
    } catch (error) {
      console.error('DashboardService.getRecentApplications error:', error);
      return []; // Return empty array instead of throwing
    }
  },

  // Get recent activities
  async getRecentActivities(): Promise<RecentActivity[]> {
    try {
      const response = await apiClient.get('/dashboard/activities/recent');
      const data = processApiResponse(response);
      return data.data || data || [];
    } catch (error) {
      console.error('DashboardService.getRecentActivities error:', error);
      return []; // Return empty array instead of throwing
    }
  },

  // Legacy method for backward compatibility
  async getDashboardStats(): Promise<any> {
    try {
      const overview = await this.getOverview();
      return overview.applications;
    } catch (error) {
      console.error('DashboardService.getDashboardStats error:', error);
      // Return default application stats instead of throwing
      return getDefaultOverview().applications;
    }
  },
};
