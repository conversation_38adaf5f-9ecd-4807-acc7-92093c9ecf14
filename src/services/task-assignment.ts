import { processApiResponse } from '@/lib/authUtils';
import { apiClient } from '../lib/apiClient';
import { PaginateQuery } from './licenseTypeService';

// Task enums matching backend
export enum TaskType {
  APPLICATION = 'application',
  COMPLAINT = 'complaint',
  DATA_BREACH = 'data_breach',
  EVALUATION = 'evaluation',
  INSPECTION = 'inspection',
  DOCUMENT_REVIEW = 'document_review',
  COMPLIANCE_CHECK = 'compliance_check',
  FOLLOW_UP = 'follow_up',
}

export enum TaskStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  ON_HOLD = 'on_hold',
}

export enum TaskPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
}

// User interface for task assignments
export interface TaskUser {
  user_id: string;
  first_name: string;
  last_name: string;
  email: string;
}

// Main Task interface matching backend entity
export interface Task {
  task_id: string;
  task_number: string;
  title: string;
  description: string;
  task_type: TaskType;
  status: TaskStatus;
  priority: TaskPriority;
  entity_type?: string;
  entity_id?: string;
  assigned_to?: string;
  assigned_by: string;
  assigned_at?: string;
  due_date?: string;
  completed_at?: string;
  review?: string;
  review_notes?: string;
  completion_notes?: string;
  created_at: string;
  created_by: string;
  updated_at: string;
  updated_by?: string;
  deleted_at?: string;
  assignee?: TaskUser;
  assigner: TaskUser;
  creator: TaskUser;
  updater?: TaskUser;
}

// Legacy interface for backward compatibility
export interface GenericTask extends Task {}

// Legacy interface for backward compatibility
export interface TaskAssignmentApplication {
  application_id: string;
  application_number: string;
  status: string;
  created_at: string;
  updated_at: string;
  applicant: {
    applicant_id: string;
    company_name: string;
    first_name: string;
    last_name: string;
  };
  license_category: {
    license_category_id: string;
    name: string;
    license_type: {
      license_type_id: string;
      name: string;
    };
  };
  assignee?: {
    user_id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  assigned_at?: string;
}

export interface TaskAssignmentOfficer {
  user_id: string;
  first_name: string;
  last_name: string;
  email: string;
  department?: string;
}

// DTOs for task operations
export interface CreateTaskDto {
  task_type: TaskType;
  title: string;
  description: string;
  priority?: TaskPriority;
  status?: TaskStatus;
  entity_type?: string;
  entity_id?: string;
  due_date?: string;
  assigned_to?: string;
  metadata?: Record<string, any>;
}

export interface UpdateTaskDto {
  title?: string;
  description?: string;
  priority?: TaskPriority;
  status?: TaskStatus;
  entity_type?: string;
  entity_id?: string;
  due_date?: string;
  review?: string;
  review_notes?: string;
  completion_notes?: string;
}

export interface AssignTaskDto {
  assignedTo: string;
  comment?: string;
}

export interface TaskFilters {
  task_type?: string;
  status?: string;
  priority?: string;
  assigned_to?: string;
  created_by?: string;
  assignment_status?: string;
}

export interface TaskStats {
  total: number;
  pending: number;
  in_progress: number;
  completed: number;
  cancelled: number;
  on_hold: number;
  unassigned: number;
  assigned: number;
  overdue: number;
}

// Legacy interfaces for backward compatibility
export interface AssignApplicationRequest {
  assignedTo: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  meta: {
    itemCount: number;
    totalItems: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
  links: {
    first: string;
    previous: string;
    next: string;
    last: string;
  };
}

// Import types from userService for compatibility
export type { PaginateQuery } from '../services/userService';

export const taskService = {
  // Main task CRUD operations
  getTasks: async (params?: PaginateQuery & TaskFilters): Promise<PaginatedResponse<Task>> => {
    // Handle assignment_status filter by using different endpoints or filters
    if (params?.assignment_status === 'unassigned') {
      // Use the unassigned tasks endpoint
      return taskService.getUnassignedTasks(params);
    } else if (params?.assignment_status === 'assigned') {
      // Use the assigned tasks endpoint instead of recursive call
      return taskService.getAssignedTasks(params);
    }

    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.search) searchParams.append('search', params.search);
    if (params?.task_type) searchParams.append('task_type', params.task_type);
    if (params?.status) searchParams.append('status', params.status);
    if (params?.priority) searchParams.append('priority', params.priority);
    if (params?.assigned_to) searchParams.append('assigned_to', params.assigned_to);
    if (params?.created_by) searchParams.append('created_by', params.created_by);

    const queryString = searchParams.toString();
    const url = `/tasks${queryString ? `?${queryString}` : ''}`;

    const response = await apiClient.get(url);
    return response.data;
  },

  getUnassignedTasks: async (params?: PaginateQuery & TaskFilters): Promise<PaginatedResponse<Task>> => {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.search) searchParams.append('search', params.search);
    if (params?.task_type) searchParams.append('task_type', params.task_type);
    if (params?.status) searchParams.append('status', params.status);
    if (params?.priority) searchParams.append('priority', params.priority);

    const queryString = searchParams.toString();
    const url = `/tasks/unassigned${queryString ? `?${queryString}` : ''}`;

    const response = await apiClient.get(url);
    return response.data;
  },

  getAssignedTasks: async (params?: PaginateQuery & TaskFilters): Promise<PaginatedResponse<Task>> => {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.search) searchParams.append('search', params.search);
    if (params?.task_type) searchParams.append('task_type', params.task_type);
    if (params?.status) searchParams.append('status', params.status);
    if (params?.priority) searchParams.append('priority', params.priority);

    const queryString = searchParams.toString();
    const url = `/tasks/assigned${queryString ? `?${queryString}` : ''}`;

    const response = await apiClient.get(url);
    return response.data;
  },

  getMyTasks: async (params?: PaginateQuery & TaskFilters): Promise<PaginatedResponse<Task>> => {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.search) searchParams.append('search', params.search);
    if (params?.task_type) searchParams.append('task_type', params.task_type);
    if (params?.status) searchParams.append('status', params.status);
    if (params?.priority) searchParams.append('priority', params.priority);

    const queryString = searchParams.toString();
    const url = `/tasks/assigned/me${queryString ? `?${queryString}` : ''}`;

    const response = await apiClient.get(url);
    return response.data;
  },

  getTaskStats: async (): Promise<TaskStats> => {
    const response = await apiClient.get('/tasks/stats');
    return response.data;
  },

  getTask: async (taskId: string): Promise<Task> => {
    const response = await apiClient.get(`/tasks/${taskId}`);
    return response.data;
  },

  createTask: async (taskData: CreateTaskDto): Promise<Task> => {
    const response = await apiClient.post('/tasks', taskData);
    return response.data;
  },

  updateTask: async (taskId: string, taskData: UpdateTaskDto): Promise<Task> => {
    const response = await apiClient.patch(`/tasks/${taskId}`, taskData);
    return response.data;
  },

  assignTask: async (taskId: string, assignData: AssignTaskDto): Promise<Task> => {
    const response = await apiClient.put(`/tasks/${taskId}/assign`, assignData);
    return response.data;
  },

  reassignTask: async (taskId: string, assignData: AssignTaskDto): Promise<Task> => {
    const response = await apiClient.put(`/tasks/${taskId}/reassign`, assignData);
    return response.data;
  },

  deleteTask: async (taskId: string): Promise<void> => {
    await apiClient.delete(`/tasks/${taskId}`);
  },

  // Get users for assignment (officers only, exclude customers)
  getUsers: async (): Promise<PaginatedResponse<TaskUser>> => {
    try {
      const response = await apiClient.get('/users', {
        params: {
          limit: 100,
          filter: {
            exclude_customers: true
          }
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching users:', error);
      return {
        data: [],
        meta: {
          itemCount: 0,
          totalItems: 0,
          itemsPerPage: 10,
          totalPages: 0,
          currentPage: 1,
        },
        links: {
          first: '',
          previous: '',
          next: '',
          last: '',
        },
      };
    }
  },

  // Get officers specifically (non-customer users)
  getOfficers: async (): Promise<PaginatedResponse<any>> => {
    try {
      const response = await apiClient.get('/users/list/officers');
      return processApiResponse(response);
    } catch (error) {
      console.error('Error fetching officers:', error);
      // Fallback to regular users endpoint with filtering
      try {
        const fallbackResponse = await apiClient.get('/users', {
          params: {
            limit: 100,
            filter: {
              exclude_customers: true
            }
          }
        });
        return processApiResponse(fallbackResponse);
      } catch (fallbackError) {
        console.error('Error fetching users as fallback:', fallbackError);
        return {
          data: [],
          meta: {
            itemCount: 0,
            totalItems: 0,
            itemsPerPage: 10,
            totalPages: 0,
            currentPage: 1,
          },
          links: {
            first: '',
            previous: '',
            next: '',
            last: '',
          },
        };
      }
    }
  },
};

// Legacy service for backward compatibility
export const taskAssignmentService = {
  // Generic task management methods
  getUnassignedTasks: taskService.getUnassignedTasks,
  getAssignedTasks: taskService.getAssignedTasks,
  assignTask: taskService.assignTask,
  getTaskById: taskService.getTask,

  // Legacy application-specific methods (for backward compatibility)
  getUnassignedApplications: async (params?: { page?: number; limit?: number; search?: string }) => {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.search) searchParams.append('search', params.search);

    const queryString = searchParams.toString();
    const url = `/applications/unassigned${queryString ? `?${queryString}` : ''}`;
    
    const response = await apiClient.get(url);
    return response.data;
  },

  // Get all applications (including assigned)
  getAllApplications: async (params?: { page?: number; limit?: number; search?: string }) => {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.search) searchParams.append('search', params.search);

    const queryString = searchParams.toString();
    const url = `/applications${queryString ? `?${queryString}` : ''}`;
    
    const response = await apiClient.get(url);
    return response.data;
  },

  // Get applications assigned to current user
  getMyAssignedApplications: async (params?: { page?: number; limit?: number; search?: string }) => {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.search) searchParams.append('search', params.search);

    const queryString = searchParams.toString();
    const url = `/applications/assigned/me${queryString ? `?${queryString}` : ''}`;
    
    const response = await apiClient.get(url);
    return response.data;
  },

  // Get officers for assignment
  getOfficers: async () => {
    try {
      const response = await apiClient.get('/users');
      return response.data;
    } catch (error) {
      console.error('Error fetching officers:', error);
      return { data: [] };
    }
  },

  // Assign application to officer
  assignApplication: async (applicationId: string, assignData: AssignApplicationRequest) => {
    const response = await apiClient.put(`/applications/${applicationId}/assign`, assignData);
    return response.data;
  },

  // Get application details
  getApplication: async (applicationId: string) => {
    const response = await apiClient.get(`/applications/${applicationId}`);
    return response.data;
  },
};