'use client';

import { useState, useEffect } from 'react';
import { applicationService } from '@/services/applicationService';
import { applicantService } from '@/services/applicantService';
import { stakeholderService } from '@/services/stakeholderService';


export interface ApplicationData {
  application?: any;
  applicant?: any;
  stakeholders?: any[];
  formData?: Record<string, any>;
}

export interface UseApplicationDataOptions {
  applicationId?: string | null;
  stepName?: string;
  autoLoad?: boolean;
}

export const useApplicationData = (options: UseApplicationDataOptions = {}) => {
  const { applicationId, stepName, autoLoad = true } = options;
  
  const [data, setData] = useState<ApplicationData>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load application data
  const loadApplicationData = async (appId: string) => {
    try {
      setLoading(true);
      setError(null);

      const applicationData: ApplicationData = {};

      // Load application details
      try {
        const application = await applicationService.getApplication(appId);
        applicationData.application = application;

        // Load applicant data if available
        if (application.applicant_id) {
          try {
            // Load stakeholders for this applicant
            try {
              const stakeholders = await stakeholderService.getStakeholdersByApplication(application.application_id);
              applicationData.stakeholders = stakeholders;
            } catch (stakeholderError) {
              console.warn('Could not load stakeholders (backend not available):', stakeholderError);
              applicationData.stakeholders = [];
            }
          } catch (applicantError) {
            console.warn('Could not load applicant:', applicantError);
          }
        }

        // Load form data for specific step if provided
        if (stepName) {
          // Form data is now loaded directly from entity-specific APIs
          // Each step loads its own data using the appropriate service
          applicationData.formData = {};
        } else {
          // Load all form data
          try {
            // TODO: Backend application-form-data endpoints not available yet
            console.warn('Application form data endpoints not implemented yet - using empty data');
            applicationData.formData = {};
            // const allFormData = await applicationFormDataService.getApplicationFormData(appId);
            // applicationData.formData = allFormData;
          } catch (formError) {
            console.warn('Could not load all form data:', formError);
            applicationData.formData = {};
          }
        }

      } catch (appError) {
        console.error('Error loading application:', appError);
        throw new Error('Failed to load application data');
      }

      setData(applicationData);
      return applicationData;

    } catch (err: any) {
      console.error('Error in loadApplicationData:', err);
      setError(err.message || 'Failed to load application data');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Auto-populate form data based on application data
  const getFormDataForStep = (stepName: string, applicationData?: ApplicationData): Record<string, any> => {
    const appData = applicationData || data;
    const formData: Record<string, any> = {};

    // Get step-specific form data first
    if (appData.formData && appData.formData[stepName]) {
      Object.assign(formData, appData.formData[stepName]);
    }

    // Auto-populate based on step type
    switch (stepName) {
      case 'applicant-info':
        if (appData.applicant) {
          Object.assign(formData, {
            name: appData.applicant.name || '',
            business_registration_number: appData.applicant.business_registration_number || '',
            tpin: appData.applicant.tpin || '',
            website: appData.applicant.website || '',
            email: appData.applicant.email || '',
            phone: appData.applicant.phone || '',
            fax: appData.applicant.fax || '',
            level_of_insurance_cover: appData.applicant.level_of_insurance_cover || '',
            date_incorporation: appData.applicant.date_incorporation || '',
            place_incorporation: appData.applicant.place_incorporation || '',
            // Address fields would be populated from related address records
          });
        }
        break;

      case 'company-profile':
        if (appData.applicant) {
          Object.assign(formData, {
            company_name: appData.applicant.name || '',
            business_registration_number: appData.applicant.business_registration_number || '',
            website: appData.applicant.website || '',
            company_email: appData.applicant.email || '',
            company_phone: appData.applicant.phone || '',
            incorporation_date: appData.applicant.date_incorporation || '',
            incorporation_place: appData.applicant.place_incorporation || '',
          });
        }
        break;

      case 'management':
        if (appData.stakeholders) {
          Object.assign(formData, {
            stakeholders: appData.stakeholders.map(stakeholder => ({
              stakeholder_id: stakeholder.stakeholder_id,
              first_name: stakeholder.first_name,
              last_name: stakeholder.last_name,
              middle_name: stakeholder.middle_name || '',
              nationality: stakeholder.nationality,
              position: stakeholder.position,
              profile: stakeholder.profile,
              contact_id: stakeholder.contact_id,
              cv_document_id: stakeholder.cv_document_id,
            }))
          });
        }
        break;

      // Add more cases for other steps as needed
      default:
        // For other steps, just use the stored form data
        break;
    }

    return formData;
  };

  // Save form data for a specific step
  const saveFormData = async (stepName: string, formData: Record<string, any>) => {
    if (!applicationId) {
      throw new Error('No application ID provided');
    }

    try {
      // Form data is now saved directly using entity-specific APIs
      // Each step handles its own data saving using the appropriate service
      console.log(`Form data for step ${stepName} should be saved using entity-specific APIs:`, formData);
      return true;
    } catch (error) {
      console.error('Error saving form data:', error);
      throw error;
    }
  };

  // Auto-load data when applicationId changes
  useEffect(() => {
    if (autoLoad && applicationId) {
      loadApplicationData(applicationId).catch(console.error);
    }
  }, [applicationId, autoLoad]);

  return {
    data,
    loading,
    error,
    loadApplicationData,
    getFormDataForStep,
    saveFormData,
    // Convenience getters
    application: data.application,
    applicant: data.applicant,
    stakeholders: data.stakeholders || [],
    formData: data.formData || {}
  };
};
