import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

/**
 * Check if a JWT token is expired
 * @param token - JWT token to check
 * @returns true if token is expired, false otherwise
 */
const isTokenExpired = (token: string): boolean => {
  if (!token) return true;

  try {
    // Decode JWT payload (without verification - just for expiry check)
    const payload = JSON.parse(atob(token.split('.')[1]));
    const currentTime = Math.floor(Date.now() / 1000);

    // Check if token has expired
    return payload.exp < currentTime;
  } catch (error) {
    console.error('Error decoding token:', error);
    return true; // Treat invalid tokens as expired
  }
};

export function middleware(request: NextRequest) {
  const url = request.nextUrl.clone();

  // Get auth tokens from cookies
  const authToken = request.cookies.get('auth_token');
  const authUser = request.cookies.get('auth_user');

  // Parse user data if available
  let user = null;


  // Try to get user from customer auth first, then staff auth
  if (authUser) {
    try {
      user = JSON.parse(authUser.value);
    } catch (error) {
      console.error('Failed to parse user data:', error);
    }
  }


  // Always allow auth routes without redirection
  if (url.pathname.startsWith('/customer/auth/') || url.pathname.startsWith('/auth/')) {
    return NextResponse.next();
  }

  // Handle root path redirections
  if (url.pathname === '/') {
    if (user && user.roles && user.roles.includes('customer')) {
      // Check if customer token is expired
      if (authToken && isTokenExpired(authToken.value)) {
        url.pathname = '/customer/auth/login';
        return NextResponse.redirect(url);
      } else if (!user.two_factor_enabled) {
        url.pathname = '/customer/auth/setup-2fa';
        return NextResponse.redirect(url);
      } else {
        url.pathname = '/customer';
        return NextResponse.redirect(url);
      }
    } else if (authToken) {
      console.log('Auth token exists')
      // Check if token is expired
      if (isTokenExpired(authToken.value)) {
        url.pathname = '/auth/verify-login';
        return NextResponse.redirect(url);
      } else {
        url.pathname = '/dashboard';
        return NextResponse.redirect(url);
      }
    } else {
      console.log('No auth token')
      url.pathname = '/customer/auth/login';
      return NextResponse.redirect(url);
    }
  }


  // Handle customer routes
  if (url.pathname.startsWith('/customer')) {
    // Check if user exists and has customer role
    if (!user || !user.roles || !user.roles.includes('customer')) {
      url.pathname = '/customer/auth/login';
      return NextResponse.redirect(url);
    }

    // For other customer routes, check authentication and token expiry
    if (!authToken || !user) {
      url.pathname = '/customer/auth/login';
      return NextResponse.redirect(url);
    }

    // Check if token is expired
    if (isTokenExpired(authToken.value)) {
      url.pathname = '/customer/auth/login';
      return NextResponse.redirect(url);
    }

    // Allow authenticated customer users to access customer portal
    return NextResponse.next();
  }

  // Handle admin/staff dashboard routes
  if (url.pathname.startsWith('/dashboard')) {
    // If user is authenticated and is a customer, redirect to customer portal
    if (user && user.roles && user.roles.includes('customer')) {
      url.pathname = '/customer';
      return NextResponse.redirect(url);
    }

    // If user is not authenticated, check referrer to determine which login page
    if (!user || !user.roles) {
      const referer = request.headers.get('referer');

      // If coming from customer auth pages, redirect to customer login
      if (referer && referer.includes('/customer/auth')) {
        url.pathname = '/customer/auth/login';
        return NextResponse.redirect(url);
      }

      // Default to admin login for dashboard access
      url.pathname = '/auth/login';
      return NextResponse.redirect(url);
    }

    // Check authentication and token expiry for admin users
    if (!authToken) {
      url.pathname = '/auth/login';
      return NextResponse.redirect(url);
    }

    // Check if token is expired
    if (isTokenExpired(authToken.value)) {
      url.pathname = '/auth/login';
      return NextResponse.redirect(url);
    }

    // Allow authenticated admin/staff users to access dashboard
    return NextResponse.next();
  }

  // Handle root path and other routes
  if (url.pathname === '/' || url.pathname === '/dashboard') {
    if (!user || !user.roles) {
      // Not authenticated, redirect to admin login
      url.pathname = '/auth/login';
      return NextResponse.redirect(url);
    }

    // Redirect based on user role
    if (user.roles.includes('customer')) {
      url.pathname = '/customer';
      return NextResponse.redirect(url);
    } else {
      // Admin/staff user
      url.pathname = '/dashboard';
      return NextResponse.redirect(url);
    }
  }

  // For other routes, allow access
  return NextResponse.next();

}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - images (public images)
     */
    '/((?!api|_next/static|_next/image|favicon.ico|images).*)',
  ],
};
