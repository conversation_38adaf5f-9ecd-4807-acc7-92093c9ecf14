'use client';

import React, { Suspense } from 'react';
import { ApplicationLayout } from '@/components/applications';
import EvaluationProgress from './EvaluationProgress';

interface EvaluationLayoutProps {
  children: React.ReactNode;
  applicationId?: string;
  licenseTypeCode?: string;
  currentStepRoute?: string;
  onNext?: () => void;
  onPrevious?: () => void;
  onSave?: () => void;
  showNextButton?: boolean;
  showPreviousButton?: boolean;
  showSaveButton?: boolean;
  nextButtonDisabled?: boolean;
  previousButtonDisabled?: boolean;
  saveButtonDisabled?: boolean;
  nextButtonText?: string;
  previousButtonText?: string;
  saveButtonText?: string;
  isSaving?: boolean;
  className?: string;
}

// Progress loading fallback
const ProgressLoadingFallback: React.FC = () => (
  <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
    <div className="animate-pulse">
      <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-4"></div>
      <div className="space-y-3">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="flex items-center space-x-3">
            <div className="h-8 w-8 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
          </div>
        ))}
      </div>
    </div>
  </div>
);

const EvaluationLayout: React.FC<EvaluationLayoutProps> = ({
  children,
  applicationId,
  licenseTypeCode,
  currentStepRoute,
  onNext,
  onPrevious,
  onSave,
  showNextButton = true,
  showPreviousButton = true,
  showSaveButton = false,
  nextButtonDisabled = false,
  previousButtonDisabled = false,
  saveButtonDisabled = false,
  nextButtonText = "Continue",
  previousButtonText = "Back",
  saveButtonText = "Save",
  isSaving = false,
  className = ''
}) => {
  return (
    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 ${className}`}>
      <div className="max-w-7xl mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Progress Steps - Left Sidebar */}
          <div className="lg:col-span-1">
            <div className="sticky top-8">
              <Suspense fallback={<ProgressLoadingFallback />}>
                <EvaluationProgress />
              </Suspense>
            </div>
          </div>

          {/* Main Content Area */}
          <div className="lg:col-span-3">
            <ApplicationLayout
              onNext={onNext}
              onPrevious={onPrevious}
              onSave={onSave}
              showNextButton={showNextButton}
              showPreviousButton={showPreviousButton}
              showSaveButton={showSaveButton}
              nextButtonDisabled={nextButtonDisabled}
              previousButtonDisabled={previousButtonDisabled}
              saveButtonDisabled={saveButtonDisabled}
              nextButtonText={nextButtonText}
              previousButtonText={previousButtonText}
              saveButtonText={saveButtonText}
              isSaving={isSaving}
              licenseTypeCode={licenseTypeCode}
              currentStepRoute={currentStepRoute}
              showStepInfo={true}
              showProgress={false} // Disable ApplicationLayout's progress since we have our own
            >
              {children}
            </ApplicationLayout>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EvaluationLayout;
