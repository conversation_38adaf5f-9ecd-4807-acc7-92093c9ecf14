'use client';

import { ApplicationStatus } from '@/types/license';
import React, { useState } from 'react';

interface EvaluationFormProps {
  applicationId: string;
  currentStep: string;
  onStatusUpdate?: (status: string, comment: string) => void;
  onCommentSave?: (comment: string) => void;
  onAttachmentUpload?: (file: File) => void;
  isSubmitting?: boolean;
  className?: string;
}

const EvaluationForm: React.FC<EvaluationFormProps> = ({
  applicationId,
  currentStep,
  onStatusUpdate,
  onCommentSave,
  onAttachmentUpload,
  isSubmitting = false,
  className = ''
}) => {
  const [comment, setComment] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<ApplicationStatus | ''>('');
  const [attachments, setAttachments] = useState<File[]>([]);

  const statusOptions = [
    { value: 'under_review', label: 'Under Review', color: 'yellow' },
    { value: 'evaluation', label: 'In Evaluation', color: 'purple' },
    { value: 'approved', label: 'Approved', color: 'green' },
    { value: 'rejected', label: 'Rejected', color: 'red' },
  ];

  const handleStatusUpdate = () => {
    if (selectedStatus && comment.trim() && onStatusUpdate) {
      onStatusUpdate(selectedStatus as ApplicationStatus, comment.trim());
    }
  };

  const handleCommentSave = () => {
    if (comment.trim() && onCommentSave) {
      onCommentSave(comment.trim());
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      const newFiles = Array.from(files);
      setAttachments(prev => [...prev, ...newFiles]);
      
      // Upload each file
      newFiles.forEach(file => {
        if (onAttachmentUpload) {
          onAttachmentUpload(file);
        }
      });
    }
  };

  const removeAttachment = (index: number) => {
    setAttachments(prev => prev.filter((_, i) => i !== index));
  };

  const getStatusColor = (status: string) => {
    const option = statusOptions.find(opt => opt.value === status);
    return option?.color || 'gray';
  };

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 ${className}`}>
      <div className="border-b border-gray-200 dark:border-gray-700 pb-4 mb-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center">
          <i className="ri-clipboard-line mr-2 text-blue-600"></i>
          Evaluation for {currentStep.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
        </h3>
        <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
          Review the information and provide your evaluation comments
        </p>
      </div>

      {/* Comment Section */}
      <div className="mb-6">
        <label htmlFor="evaluation-comment" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Evaluation Comments *
        </label>
        <textarea
          id="evaluation-comment"
          value={comment}
          onChange={(e) => setComment(e.target.value)}
          rows={4}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-gray-100"
          placeholder="Enter your evaluation comments for this step..."
          required
        />
        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
          Provide detailed feedback about this section of the application
        </p>
      </div>

      {/* Status Update Section */}
      <div className="mb-6">
        <label htmlFor="status-select" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Update Application Status
        </label>
        <select
          id="status-select"
          value={selectedStatus}
          onChange={(e) => setSelectedStatus(e.target.value as ApplicationStatus)}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-gray-100"
        >
          <option value="">Select status...</option>
          {statusOptions.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      </div>

      {/* File Upload Section */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Attach Supporting Documents
        </label>
        <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-4">
          <input
            type="file"
            multiple
            onChange={handleFileUpload}
            className="hidden"
            id="file-upload"
            accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
          />
          <label
            htmlFor="file-upload"
            className="cursor-pointer flex flex-col items-center justify-center"
          >
            <i className="ri-upload-cloud-line text-3xl text-gray-400 mb-2"></i>
            <span className="text-sm text-gray-600 dark:text-gray-400">
              Click to upload files or drag and drop
            </span>
            <span className="text-xs text-gray-500 dark:text-gray-500 mt-1">
              PDF, DOC, DOCX, JPG, PNG up to 10MB each
            </span>
          </label>
        </div>

        {/* Uploaded Files List */}
        {attachments.length > 0 && (
          <div className="mt-4">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Uploaded Files ({attachments.length})
            </h4>
            <div className="space-y-2">
              {attachments.map((file, index) => (
                <div key={index} className="flex items-center justify-between bg-gray-50 dark:bg-gray-700 p-2 rounded">
                  <div className="flex items-center">
                    <i className="ri-file-line text-gray-400 mr-2"></i>
                    <span className="text-sm text-gray-700 dark:text-gray-300">{file.name}</span>
                    <span className="text-xs text-gray-500 dark:text-gray-400 ml-2">
                      ({(file.size / 1024 / 1024).toFixed(2)} MB)
                    </span>
                  </div>
                  <button
                    type="button"
                    onClick={() => removeAttachment(index)}
                    className="text-red-500 hover:text-red-700"
                  >
                    <i className="ri-close-line"></i>
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Action Buttons */}
      <div className="flex flex-wrap gap-3">
        <button
          type="button"
          onClick={handleCommentSave}
          disabled={!comment.trim() || isSubmitting}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
        >
          <i className="ri-save-line mr-2"></i>
          Save Comment
        </button>

        <button
          type="button"
          onClick={handleStatusUpdate}
          disabled={!selectedStatus || !comment.trim() || isSubmitting}
          className={`px-4 py-2 rounded-md text-white flex items-center disabled:opacity-50 disabled:cursor-not-allowed ${
            selectedStatus === 'approved' ? 'bg-green-600 hover:bg-green-700' :
            selectedStatus === 'rejected' ? 'bg-red-600 hover:bg-red-700' :
            'bg-purple-600 hover:bg-purple-700'
          }`}
        >
          <i className="ri-check-line mr-2"></i>
          Update Status
        </button>

        {isSubmitting && (
          <div className="flex items-center text-gray-500 dark:text-gray-400">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-500 mr-2"></div>
            Processing...
          </div>
        )}
      </div>
    </div>
  );
};

export default EvaluationForm;
