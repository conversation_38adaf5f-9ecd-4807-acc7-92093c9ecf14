'use client';

import React, { useState, useEffect } from 'react';
import { consumerAffairsService, ConsumerAffairsComplaint } from '@/services/consumer-affairs';
import { useToast } from '@/contexts/ToastContext';
import Loader from '@/components/Loader';

interface ConsumerAffairsViewModalProps {
  isOpen: boolean;
  onClose: () => void;
  complaintId: string | null;
  onUpdate?: () => void;
}

const ConsumerAffairsViewModal: React.FC<ConsumerAffairsViewModalProps> = ({
  isOpen,
  onClose,
  complaintId,
  onUpdate
}) => {
  const { showSuccess, showError } = useToast();
  const [complaint, setComplaint] = useState<ConsumerAffairsComplaint | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (isOpen && complaintId) {
      fetchComplaintDetails();
    }
  }, [isOpen, complaintId]);

  const fetchComplaintDetails = async () => {
    if (!complaintId) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const response = await consumerAffairsService.getComplaintById(complaintId);
      setComplaint(response);
    } catch (err: unknown) {
      console.error('Error fetching complaint details:', err);
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(`Failed to load complaint details: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'submitted': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'under_review': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'investigating': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case 'resolved': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'closed': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority?.toLowerCase()) {
      case 'low': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'high': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case 'urgent': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div className="mt-3">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
              Consumer Affairs Complaint Details
            </h3>
            <button
              type="button"
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <i className="ri-close-line text-xl"></i>
            </button>
          </div>

          {/* Content */}
          {loading ? (
            <div className="py-8">
              <Loader message="Loading complaint details..." />
            </div>
          ) : error ? (
            <div className="bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md p-4">
              <div className="flex">
                <i className="ri-error-warning-line text-red-400 mr-2"></i>
                <p className="text-red-700 dark:text-red-200">{error}</p>
              </div>
            </div>
          ) : complaint ? (
            <div className="space-y-6">
              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                    Complaint Number
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {complaint.complaint_number}
                  </p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                    Status
                  </h4>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(complaint.status)}`}>
                    {complaint.status?.replace('_', ' ').toUpperCase()}
                  </span>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                    Category
                  </h4>
                  <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                    {complaint.category}
                  </span>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                    Priority
                  </h4>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(complaint.priority)}`}>
                    {complaint.priority?.toUpperCase() || 'MEDIUM'}
                  </span>
                </div>
              </div>

              {/* Title and Description */}
              <div>
                <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                  Title
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {complaint.title}
                </p>
              </div>

              <div>
                <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                  Description
                </h4>
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <p className="text-sm text-gray-600 dark:text-gray-400 whitespace-pre-wrap">
                    {complaint.description}
                  </p>
                </div>
              </div>

              {/* Complainant Information */}
              {complaint.complainant && (
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                    Complainant
                  </h4>
                  <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      <strong>Name:</strong> {complaint.complainant.first_name} {complaint.complainant.last_name}
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      <strong>Email:</strong> {complaint.complainant.email}
                    </p>
                  </div>
                </div>
              )}

              {/* Assignment Information */}
              {complaint.assignee && (
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                    Assigned To
                  </h4>
                  <div className="bg-green-50 dark:bg-green-900 rounded-lg p-4">
                    <p className="text-sm text-green-700 dark:text-green-200">
                      <strong>Officer:</strong> {complaint.assignee.first_name} {complaint.assignee.last_name}
                    </p>
                    <p className="text-sm text-green-700 dark:text-green-200">
                      <strong>Email:</strong> {complaint.assignee.email}
                    </p>
                  </div>
                </div>
              )}

              {/* Resolution */}
              {complaint.resolution && (
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                    Resolution
                  </h4>
                  <div className="bg-green-50 dark:bg-green-900 rounded-lg p-4">
                    <p className="text-sm text-green-700 dark:text-green-200 whitespace-pre-wrap">
                      {complaint.resolution}
                    </p>
                  </div>
                </div>
              )}

              {/* Internal Notes */}
              {complaint.internal_notes && (
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                    Internal Notes
                  </h4>
                  <div className="bg-yellow-50 dark:bg-yellow-900 rounded-lg p-4">
                    <p className="text-sm text-yellow-700 dark:text-yellow-200 whitespace-pre-wrap">
                      {complaint.internal_notes}
                    </p>
                  </div>
                </div>
              )}

              {/* Timestamps */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 pt-4 border-t border-gray-200 dark:border-gray-600">
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                    Submitted
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {new Date(complaint.created_at).toLocaleString()}
                  </p>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                    Last Updated
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {new Date(complaint.updated_at).toLocaleString()}
                  </p>
                </div>
                {complaint.resolved_at && (
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                      Resolved
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {new Date(complaint.resolved_at).toLocaleString()}
                    </p>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-500 dark:text-gray-400">No complaint data available</p>
            </div>
          )}

          {/* Actions */}
          <div className="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            >
              Close
            </button>
            {complaint && (
              <button
                type="button"
                onClick={() => {
                  // For now, just show a message. In the future, this could navigate to an evaluation page
                  showSuccess('Complaint evaluation feature coming soon');
                }}
                className="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
              >
                <i className="ri-clipboard-line mr-2"></i>
                Evaluate
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConsumerAffairsViewModal;
