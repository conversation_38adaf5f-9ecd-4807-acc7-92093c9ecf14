'use client';

import React, { useState, useEffect } from 'react';
import { usePathname } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import NavItem from './NavItem';
import Link from 'next/link';
import { getUserInitials } from '@/utils/imageUtils';
import { licenseTypeService, NavigationItem } from '@/services/licenseTypeService';

const Sidebar: React.FC = () => {
  const { user } = useAuth();
  const pathname = usePathname();
  const [isMobileOpen, setIsMobileOpen] = useState(false);
  const [licenseTypeNavItems, setLicenseTypeNavItems] = useState<NavigationItem[]>([]);
  const [loadingLicenseTypes, setLoadingLicenseTypes] = useState(true);

  // Close mobile sidebar when route changes
  useEffect(() => {
    setIsMobileOpen(false);
  }, [pathname]);

  // Load license types for navigation
  useEffect(() => {
    const loadLicenseTypes = async () => {
      try {
        setLoadingLicenseTypes(true);
        const data = await licenseTypeService.getNavigationItems();
        const navigationItems: NavigationItem[] = data.data;
        setLicenseTypeNavItems(navigationItems);
      } catch (error) {
        console.error('Failed to load license types for navigation:', error);
        // Fallback to empty array if API fails
        setLicenseTypeNavItems([]);
      } finally {
        setLoadingLicenseTypes(false);
      }
    };

    loadLicenseTypes();
  }, []);

  // Close mobile sidebar when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const sidebar = document.getElementById('mobile-sidebar');
      const toggleButton = document.getElementById('mobile-sidebar-toggle');
      
      if (
        isMobileOpen &&
        sidebar &&
        !sidebar.contains(event.target as Node) &&
        toggleButton &&
        !toggleButton.contains(event.target as Node)
      ) {
        setIsMobileOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isMobileOpen]);

  const toggleMobileSidebar = () => {
    setIsMobileOpen(!isMobileOpen);
  };

  // Static navigation items
  const topStaticNavigationItems = [
    {
      href: '/dashboard',
      icon: 'ri-dashboard-line',
      label: 'Dashboard',
      roles: ['administrator', 'evaluator', 'customer']
    },
    {
      href: '/tasks',
      icon: 'ri-user-add-line',
      label: 'Tasks',
      roles: ['administrator', 'all']
    }
  ];

  const staticNavigationItems = [

    {
      href: '/consumer-affairs',
      icon: 'ri-shield-user-line',
      label: 'Consumer Affairs',
      roles: ['administrator', 'evaluator', 'customer']
    },
    {
      href: '/data-breach',
      icon: 'ri-shield-cross-line',
      label: 'Data Breach',
      roles: ['administrator', 'evaluator']
    },
    {
      href: '/resources',
      icon: 'ri-folder-line',
      label: 'Resources',
      roles: ['administrator', 'evaluator']
    },
    {
      href: '/procurement',
      icon: 'ri-shopping-bag-line',
      label: 'Procurement',
      roles: ['administrator', 'evaluator']
    },
    {
      href: '/financial',
      icon: 'ri-money-dollar-circle-line',
      label: 'Accounts & Finance',
      roles: ['administrator', 'evaluator']
    },
    {
      href: '/reports',
      icon: 'ri-file-chart-line',
      label: 'Reports & Analytics',
      roles: ['administrator', 'evaluator']
    }
  ];

  const settingsNavigationItems = [
    {
      href: '/users',
      icon: 'ri-user-settings-line',
      label: 'User Management',
      roles: ['administrator']
    },
    {
      href: '/settings',
      icon: 'ri-settings-3-line',
      label: 'Management Settings',
      roles: ['administrator']
    },
    {
      href: '/audit-trail',
      icon: 'ri-shield-line',
      label: 'Audit Trail',
      roles: ['administrator', 'evaluator']
    },
    {
      href: '/help',
      icon: 'ri-question-line',
      label: 'Help & Support',
      roles: ['administrator', 'evaluator', 'customer']
    }
  ];

  // Combine static navigation items with dynamic license types
  const mainNavigationItems = [
    ...licenseTypeNavItems.map(item => ({
      href: item.href,
      icon: 'ri-file-list-line', // Generic icon for all license types
      label: item.label,
      roles: item.roles
    })),
    ...staticNavigationItems
  ];

  const filteredMainNavItems = mainNavigationItems.filter(item =>
    user?.roles?.some(role => item.roles.includes(role)) ||
    item.roles.includes('customer')
  );

  const filteredSettingsNavItems = settingsNavigationItems.filter(item =>
    user?.roles?.some(role => item.roles.includes(role)) ||
    item.roles.includes('customer')
  );

  return (
    <>
      {/* Mobile Sidebar Toggle Button */}
      <button
        id="mobile-sidebar-toggle"
        onClick={toggleMobileSidebar}
        className="lg:hidden fixed top-4 left-4 z-50 p-2 bg-primary text-white rounded-md shadow-lg hover:bg-red-700 transition-colors"
        aria-label="Toggle mobile sidebar"
      >
        <i className={`fas ${isMobileOpen ? 'fa-times' : 'fa-bars'}`}></i>
      </button>

      {/* Mobile Overlay */}
      {isMobileOpen && (
        <div 
          className="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={() => setIsMobileOpen(false)}
        />
      )}

      {/* Sidebar */}
      <aside
        id="mobile-sidebar"
        className={`
          fixed lg:static inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out
          ${isMobileOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
        `}
      >
        <div className="flex flex-col h-full">
          {/* Logo/Header */}
          <div className="h-16 flex items-center px-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center">
              <img src="/images/macra-logo.png" alt="MACRA Logo" className="max-h-12 w-auto" />
            </div>
          </div>

          {/* Navigation */}
          <nav className="mt-6 px-4 side-nav">
            {/* Main Navigation */}
            {topStaticNavigationItems.length > 0 && (
              <div className="mt-2 space-y-1">
                {topStaticNavigationItems.map((item) => (
                  <NavItem
                    key={item.href}
                    href={item.href}
                    icon={item.icon}
                    label={item.label}
                    isActive={pathname === item.href}
                    onClick={() => setIsMobileOpen(false)}
                  />
              ))}
            </div>
            )}

            {/* Main Navigation */}
            {filteredMainNavItems.length > 0 && (
              <div className="mt-8">
                <h3 className="px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Main Menu
                </h3>
                <div className="mt-2 space-y-1">
                  {filteredMainNavItems.map((item) => (
                    <NavItem
                      key={item.href}
                      href={item.href}
                      icon={item.icon}
                      label={item.label}
                      isActive={pathname === item.href}
                      onClick={() => setIsMobileOpen(false)}
                    />
                ))}
                </div>
              </div>
            )}

            {/* Settings Section */}
            {filteredSettingsNavItems.length > 0 && (
              <div className="mt-8">
                <h3 className="px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Settings
                </h3>
                <div className="mt-2 space-y-1">
                  {filteredSettingsNavItems.map((item) => (
                    <NavItem
                      key={item.href}
                      href={item.href}
                      icon={item.icon}
                      label={item.label}
                      isActive={pathname === item.href}
                      onClick={() => setIsMobileOpen(false)}
                    />
                  ))}
                </div>
              </div>
            )}
          </nav>

          {/* User Info */}
          {user && (
            <div className="absolute bottom-0 w-64 p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
              <div className="flex items-center space-x-3">
                {user.profile_image ? (
                  <img
                    className="h-10 w-10 rounded-full object-cover"
                    src={user.profile_image}
                    alt="Profile"
                    onError={(e) => {
                      // Fallback to initials if image fails to load
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                      target.nextElementSibling?.classList.remove('hidden');
                    }}
                  />
                ) : null}
                <div className={`h-10 w-10 rounded-full bg-red-600 flex items-center justify-center text-white text-sm font-medium ${user.profile_image ? 'hidden' : ''}`}>
                  {getUserInitials(user.first_name, user.last_name)}
                </div>
                <Link href='/profile' className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                    {user.first_name || 'Unknown'} {user.last_name || 'User'}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                    {user.roles && user.roles.length > 0
                      ? user.roles.map(role =>
                          typeof role === 'string'
                            ? role.replace(/_/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase())
                            : 'Unknown'
                        ).join(', ')
                      : 'User'
                    }
                  </p>
                </Link>
              </div>
            </div>
          )}
        </div>
      </aside>
    </>
  );
};

export default Sidebar;