'use client';

import { useState, useEffect } from 'react';
import { roleService, RolesResponse } from '../../services/roleService';
import { Role, PaginateQuery } from '../../services/userService';
import DataTable from '../common/DataTable';
import ConfirmationModal from '../common/ConfirmationModal';

interface RolesTabProps {
  onEditRole: (role: Role) => void;
  onCreateRole: () => void;
}

const RolesTab = ({ onEditRole, onCreateRole }: RolesTabProps) => {
  const [rolesData, setRolesData] = useState<RolesResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [roleToDelete, setRoleToDelete] = useState<Role | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    loadRoles({ page: 1, limit: 10 });
  }, []);

  const loadRoles = async (query: PaginateQuery) => {
    try {
      setLoading(true);
      const response = await roleService.getRoles(query);
      setRolesData(response);
    } catch (err) {
      console.error('Error loading roles:', err);
      // Set empty data structure to prevent undefined errors
      setRolesData({
        data: [],
        meta: {
          itemsPerPage: query.limit || 10,
          totalItems: 0,
          currentPage: query.page || 1,
          totalPages: 0,
          sortBy: [],
          searchBy: [],
          search: '',
          select: [],
        },
        links: {
          current: '',
        },
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteRole = (role: Role) => {
    setRoleToDelete(role);
    setShowDeleteModal(true);
  };

  const handleConfirmDelete = async () => {
    if (!roleToDelete) return;

    setIsDeleting(true);
    try {
      await roleService.deleteRole(roleToDelete.role_id);
      if (rolesData) {
        loadRoles({ page: rolesData.meta.currentPage, limit: rolesData.meta.itemsPerPage });
      }
      setShowDeleteModal(false);
      setRoleToDelete(null);
    } catch (err) {
      setError('Failed to delete role');
      console.error('Error deleting role:', err);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleCancelDelete = () => {
    setShowDeleteModal(false);
    setRoleToDelete(null);
  };

  // Define columns for roles table
  const roleColumns = [
    {
      key: 'name',
      label: 'Role Name',
      render: (value: string) => (
        <div className="text-sm font-medium text-gray-900 dark:text-gray-100 capitalize">
          {value}
        </div>
      ),
    },
    {
      key: 'description',
      label: 'Description',
      render: (value: string) => (
        <div className="text-sm text-gray-900 dark:text-gray-100">
          {value || 'No description'}
        </div>
      ),
    },
    {
      key: 'created_at',
      label: 'Created Date',
      render: (value: string) => (
        <span className="text-sm text-gray-500 dark:text-gray-400">
          {value ? new Date(value).toLocaleDateString() : 'N/A'}
        </span>
      ),
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (value: any, role: Role) => (
        <div className="flex items-center space-x-2">
          <button
            onClick={() => onEditRole(role)}
            className="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 p-1 rounded hover:bg-indigo-50 dark:hover:bg-indigo-900"
            title="Edit role"
          >
            <i className="ri-edit-line"></i>
          </button>
          <button
            onClick={() => handleDeleteRole(role)}
            className="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 p-1 rounded hover:bg-red-50 dark:hover:bg-red-900"
            title="Delete role"
          >
            <i className="ri-delete-bin-line"></i>
          </button>
        </div>
      ),
    },
  ];

  return (
    <div>
      {/* Page header */}
      <div className="mb-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div>
            <h1 className="text-2xl font-semibold text-gray-900 dark:text-gray-100">Roles</h1>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Manage user roles and their permissions.
            </p>
          </div>
          <div className="flex space-x-3 place-content-start">
            <div className="relative">
              <button
                onClick={onCreateRole}
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-900"
              >
                <div className="w-5 h-5 flex items-center justify-center mr-2">
                  <i className="ri-add-line"></i>
                </div>
                Add Role
              </button>
            </div>
          </div>
        </div>
      </div>

      {error && (
        <div className="mb-4 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-200 px-4 py-3 rounded">
          {error}
        </div>
      )}

      <DataTable
        columns={roleColumns}
        data={rolesData}
        loading={loading}
        onQueryChange={(query) => {
          loadRoles({
            page: query.page,
            limit: query.limit,
            search: query.search,
            sortBy: query.sortBy,
          });
        }}
        searchPlaceholder="Search roles by name or description..."
      />

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={showDeleteModal}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title="Delete Role"
        message={
          roleToDelete ? (
            <div>
              <p className="mb-2">
                Are you sure you want to delete the role <strong>{roleToDelete.name}</strong>?
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                This action cannot be undone. Users with this role will lose their associated permissions.
              </p>
            </div>
          ) : (
            'Are you sure you want to delete this role?'
          )
        }
        confirmText="Yes, Delete Role"
        cancelText="Cancel"
        confirmVariant="danger"
        loading={isDeleting}
      />
    </div>
  );
};

export default RolesTab;
