'use client';

import React, { useState, useEffect } from 'react';
import { dataBreachService, DataBreachReport } from '@/services/data-breach';
import { useToast } from '@/contexts/ToastContext';
import ComplaintStatusBar, { COMPLAINT_STAGES, getStageIndexFromStatus } from '@/components/customer/ComplaintStatusBar';

interface DataBreachViewModalProps {
  isOpen: boolean;
  onClose: () => void;
  reportId: string | null;
  onUpdate?: () => void;
}

const DataBreachViewModal: React.FC<DataBreachViewModalProps> = ({
  isOpen,
  onClose,
  reportId,
  onUpdate
}) => {
  const { showSuccess, showError } = useToast();
  const [report, setReport] = useState<DataBreachReport | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [isAssigning, setIsAssigning] = useState(false);
  const [assignedOfficer, setAssignedOfficer] = useState('');

  useEffect(() => {
    if (isOpen && reportId) {
      fetchReportDetails();
    }
  }, [isOpen, reportId]);

  const fetchReportDetails = async () => {
    if (!reportId) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const response = await dataBreachService.getReportById(reportId);
      setReport(response);
      setAssignedOfficer(response.assigned_to || '');
    } catch (err: unknown) {
      console.error('Error fetching report details:', err);
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(`Failed to load report details: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  const handleAssignOfficer = async () => {
    if (!reportId || !assignedOfficer.trim()) {
      showError('Please enter an officer name');
      return;
    }

    setIsAssigning(true);
    try {
      await dataBreachService.assignReport(reportId, assignedOfficer);
      showSuccess('Officer assigned successfully');
      
      // Update local state
      if (report) {
        setReport({ ...report, assigned_to: assignedOfficer });
      }
      
      if (onUpdate) onUpdate();
    } catch (err: unknown) {
      console.error('Error assigning officer:', err);
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      showError(`Failed to assign officer: ${errorMessage}`);
    } finally {
      setIsAssigning(false);
    }
  };

  const handleCloseReport = async () => {
    if (!reportId) return;

    try {
      await dataBreachService.updateStatus(reportId, 'closed');
      showSuccess('Report closed successfully');
      
      // Update local state
      if (report) {
        setReport({ ...report, status: 'closed' });
      }
      
      if (onUpdate) onUpdate();
    } catch (err: unknown) {
      console.error('Error closing report:', err);
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      showError(`Failed to close report: ${errorMessage}`);
    }
  };

  const handleClose = () => {
    setReport(null);
    setError(null);
    setActiveTab('overview');
    setAssignedOfficer('');
    onClose();
  };

  if (!isOpen) return null;

  const tabs = [
    { id: 'overview', label: 'Overview', icon: 'ri-file-text-line' },
    { id: 'details', label: 'Report Details', icon: 'ri-information-line' },
    { id: 'actions', label: 'Actions', icon: 'ri-settings-line' }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'submitted': return 'bg-blue-100 text-blue-800';
      case 'under_review': return 'bg-yellow-100 text-yellow-800';
      case 'investigating': return 'bg-orange-100 text-orange-800';
      case 'resolved': return 'bg-green-100 text-green-800';
      case 'closed': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'critical': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const renderOverviewTab = () => (
    <div className="space-y-6">
      {/* Status Progress */}
      <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
        <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-4">Investigation Progress</h4>
        <ComplaintStatusBar
          currentStage={getStageIndexFromStatus(report?.status || 'submitted')}
          stages={COMPLAINT_STAGES.DATA_BREACH}
          status={report?.status as 'submitted' | 'under_review' | 'investigating' | 'resolved' | 'closed'}
          size="sm"
          variant="horizontal"
          showPercentage={true}
          showStageNames={true}
        />
      </div>

      {/* Basic Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
          <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">Report Information</h4>
          <div className="space-y-2 text-sm">
            <div><span className="font-medium">Report ID:</span> {report?.report_id}</div>
            <div><span className="font-medium">Report Number:</span> {report?.report_number}</div>
            <div><span className="font-medium">Status:</span> 
              <span className={`ml-2 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(report?.status || '')}`}>
                {report?.status?.replace('_', ' ').toUpperCase()}
              </span>
            </div>
            <div><span className="font-medium">Severity:</span> 
              <span className={`ml-2 px-2 py-1 rounded-full text-xs font-medium ${getSeverityColor(report?.severity || '')}`}>
                {report?.severity?.toUpperCase()}
              </span>
            </div>
            <div><span className="font-medium">Category:</span> {report?.category}</div>
            <div><span className="font-medium">Incident Date:</span> {report?.incident_date ? new Date(report.incident_date).toLocaleDateString() : 'N/A'}</div>
          </div>
        </div>

        <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
          <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">Reporter Information</h4>
          <div className="space-y-2 text-sm">
            <div><span className="font-medium">Name:</span> {report?.reporter ? `${report.reporter.first_name} ${report.reporter.last_name}` : 'N/A'}</div>
            <div><span className="font-medium">Email:</span> {report?.reporter?.email || 'N/A'}</div>
            <div><span className="font-medium">Reported:</span> {report?.created_at ? new Date(report.created_at).toLocaleDateString() : 'N/A'}</div>
            <div><span className="font-medium">Last Updated:</span> {report?.updated_at ? new Date(report.updated_at).toLocaleDateString() : 'N/A'}</div>
            <div><span className="font-medium">Assigned To:</span> {report?.assigned_to || 'Unassigned'}</div>
          </div>
        </div>
      </div>

      {/* Organization and Impact */}
      <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
        <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">Incident Details</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div><span className="font-medium">Organization Involved:</span> {report?.organization_involved || 'N/A'}</div>
          <div><span className="font-medium">Affected Data Types:</span> {report?.affected_data_types || 'N/A'}</div>
        </div>
        {report?.contact_attempts && (
          <div className="mt-3">
            <span className="font-medium">Contact Attempts:</span>
            <p className="mt-1 text-gray-600 dark:text-gray-400">{report.contact_attempts}</p>
          </div>
        )}
      </div>
    </div>
  );

  const renderDetailsTab = () => (
    <div className="space-y-6">
      <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
        <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">Incident Title</h4>
        <p className="text-gray-700 dark:text-gray-300">{report?.title}</p>
      </div>

      <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
        <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">Detailed Description</h4>
        <p className="text-gray-700 dark:text-gray-300 whitespace-pre-wrap">{report?.description}</p>
      </div>

      {report?.resolution && (
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 p-4 rounded-lg">
          <h4 className="text-sm font-medium text-green-800 dark:text-green-300 mb-3">Resolution</h4>
          <p className="text-green-700 dark:text-green-400 whitespace-pre-wrap">{report.resolution}</p>
        </div>
      )}

      {report?.internal_notes && (
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 p-4 rounded-lg">
          <h4 className="text-sm font-medium text-yellow-800 dark:text-yellow-300 mb-3">Internal Notes</h4>
          <p className="text-yellow-700 dark:text-yellow-400 whitespace-pre-wrap">{report.internal_notes}</p>
        </div>
      )}
    </div>
  );

  const renderActionsTab = () => (
    <div className="space-y-6">
      {/* Assign Officer */}
      <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
        <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">Assign Officer</h4>
        <div className="flex gap-3">
          <input
            type="text"
            value={assignedOfficer}
            onChange={(e) => setAssignedOfficer(e.target.value)}
            placeholder="Enter officer name"
            className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm dark:bg-gray-800 dark:text-gray-100"
          />
          <button
            type="button"
            onClick={handleAssignOfficer}
            disabled={isAssigning || !assignedOfficer.trim()}
            className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isAssigning ? 'Assigning...' : 'Assign'}
          </button>
        </div>
      </div>

      {/* Close Report */}
      <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
        <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">Close Report</h4>
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
          Mark this report as closed. This action cannot be undone.
        </p>
        <button
          type="button"
          onClick={handleCloseReport}
          disabled={report?.status === 'closed'}
          className="px-4 py-2 bg-red-600 text-white rounded-md text-sm hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {report?.status === 'closed' ? 'Already Closed' : 'Close Report'}
        </button>
      </div>
    </div>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return renderOverviewTab();
      case 'details':
        return renderDetailsTab();
      case 'actions':
        return renderActionsTab();
      default:
        return renderOverviewTab();
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg w-full max-w-6xl max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Data Breach Report Details
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              {report?.report_number || 'Loading...'}
            </p>
          </div>
          <button
            type="button"
            onClick={handleClose}
            aria-label="Close modal"
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <i className="ri-close-line text-xl"></i>
          </button>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="flex space-x-8 px-6" aria-label="Tabs">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:border-gray-300'
                }`}
              >
                <i className={`${tab.icon} mr-2`}></i>
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-3 text-gray-600 dark:text-gray-400">Loading report details...</span>
            </div>
          ) : error ? (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
              <div className="flex">
                <i className="ri-error-warning-line text-red-400 mr-2"></i>
                <p className="text-red-700 dark:text-red-200">{error}</p>
              </div>
            </div>
          ) : report ? (
            renderTabContent()
          ) : (
            <div className="text-center py-12">
              <p className="text-gray-500 dark:text-gray-400">No report data available</p>
            </div>
          )}

          {/* Actions */}
          <div className="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
            <button
              type="button"
              onClick={handleClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            >
              Close
            </button>
            {report && (
              <button
                type="button"
                onClick={() => {
                  // For now, just show a message. In the future, this could navigate to an evaluation page
                  showSuccess('Data breach evaluation feature coming soon');
                }}
                className="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
              >
                <i className="ri-clipboard-line mr-2"></i>
                Evaluate
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DataBreachViewModal;
