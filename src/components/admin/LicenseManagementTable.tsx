import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { UserPlus, Eye, CheckCircle } from 'lucide-react';
import { TaskAssignmentModal } from '@/components/shared/TaskAssignmentModal';
import { useTaskAssignment } from '@/hooks/useTaskAssignment';
import { toast } from 'sonner';

interface Application {
  application_id: string;
  application_number: string;
  status: string;
  assigned_to?: string;
  assignee?: {
    user_id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  applicant?: {
    name: string;
    email: string;
  };
  license_category?: {
    name: string;
    license_category_id: string;
    license_type?: {
      name: string;
      code: string;
    };
  };
  created_at: string;
  submitted_at?: string;
}

interface User {
  user_id: string;
  first_name: string;
  last_name: string;
  email: string;
  role?: {
    name: string;
  };
}

interface LicenseManagementTableProps {
  applications: Application[];
  users: User[];
  onRefresh: () => void;
  loading?: boolean;
}

export const LicenseManagementTable: React.FC<LicenseManagementTableProps> = ({
  applications,
  users,
  onRefresh,
  loading = false,
}) => {
  const [selectedApplication, setSelectedApplication] = useState<Application | null>(null);
  const [isAssignModalOpen, setIsAssignModalOpen] = useState(false);

  const { assignFromLicenseManagement, isAssigning } = useTaskAssignment({
    onSuccess: () => {
      onRefresh();
      setIsAssignModalOpen(false);
      setSelectedApplication(null);
    },
  });

  const handleAssignClick = (application: Application) => {
    setSelectedApplication(application);
    setIsAssignModalOpen(true);
  };

  const handleAssignment = async (assignmentData: any) => {
    if (!selectedApplication) return;

    await assignFromLicenseManagement(
      selectedApplication.application_id,
      selectedApplication.application_number,
      assignmentData
    );
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      draft: { variant: 'secondary' as const, label: 'Draft' },
      submitted: { variant: 'default' as const, label: 'Submitted' },
      evaluation: { variant: 'warning' as const, label: 'Under Evaluation' },
      approved: { variant: 'success' as const, label: 'Approved' },
      rejected: { variant: 'destructive' as const, label: 'Rejected' },
      pending: { variant: 'outline' as const, label: 'Pending' },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const canAssign = (application: Application) => {
    return application.status === 'submitted' && !application.assigned_to;
  };

  const isAssigned = (application: Application) => {
    return !!application.assigned_to;
  };

  return (
    <>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Application #</TableHead>
              <TableHead>Applicant</TableHead>
              <TableHead>License Type</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Assigned To</TableHead>
              <TableHead>Submitted</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {applications.map((application) => (
              <TableRow key={application.application_id}>
                <TableCell className="font-medium">
                  {application.application_number}
                </TableCell>
                <TableCell>
                  <div>
                    <div className="font-medium">{application.applicant?.name}</div>
                    <div className="text-sm text-gray-500">{application.applicant?.email}</div>
                  </div>
                </TableCell>
                <TableCell>
                  <div>
                    <div className="font-medium">
                      {application.license_category?.license_type?.name}
                    </div>
                    <div className="text-sm text-gray-500">
                      {application.license_category?.name}
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  {getStatusBadge(application.status)}
                </TableCell>
                <TableCell>
                  {isAssigned(application) ? (
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <div>
                        <div className="font-medium text-sm">
                          {application.assignee?.first_name} {application.assignee?.last_name}
                        </div>
                        <div className="text-xs text-gray-500">
                          {application.assignee?.email}
                        </div>
                      </div>
                    </div>
                  ) : (
                    <span className="text-gray-400 text-sm">Unassigned</span>
                  )}
                </TableCell>
                <TableCell>
                  {application.submitted_at ? (
                    <div className="text-sm">
                      {new Date(application.submitted_at).toLocaleDateString()}
                    </div>
                  ) : (
                    <span className="text-gray-400 text-sm">Not submitted</span>
                  )}
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex items-center justify-end gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        // Navigate to application evaluation if it has license type code
                        const licenseTypeCode = application.license_category?.license_type?.code;
                        if (licenseTypeCode) {
                          const evaluationUrl = `/applications/${licenseTypeCode}/evaluate/applicant-info?application_id=${application.application_id}&license_category_id=${application.license_category_id}`;
                          window.open(evaluationUrl, '_blank');
                        } else {
                          // Fallback to general application view
                          window.open(`/admin/applications/${application.application_id}`, '_blank');
                        }
                      }}
                    >
                      <Eye className="h-4 w-4 mr-1" />
                      View
                    </Button>
                    
                    {canAssign(application) && (
                      <Button
                        variant="default"
                        size="sm"
                        onClick={() => handleAssignClick(application)}
                        disabled={isAssigning}
                      >
                        <UserPlus className="h-4 w-4 mr-1" />
                        Assign
                      </Button>
                    )}
                    
                    {isAssigned(application) && (
                      <Badge variant="outline" className="text-green-600 border-green-600">
                        Assigned
                      </Badge>
                    )}
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Task Assignment Modal */}
      <TaskAssignmentModal
        isOpen={isAssignModalOpen}
        onClose={() => {
          setIsAssignModalOpen(false);
          setSelectedApplication(null);
        }}
        onAssign={handleAssignment}
        users={users}
        loading={isAssigning}
        title={`Assign Application ${selectedApplication?.application_number}`}
      />
    </>
  );
};
