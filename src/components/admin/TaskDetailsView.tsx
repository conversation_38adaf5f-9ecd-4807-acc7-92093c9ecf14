import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import {
  User,
  Calendar,
  Clock,
  AlertTriangle,
  FileText,
  ExternalLink,
  UserPlus,
} from 'lucide-react';
import { format } from 'date-fns';
import { TaskAssignmentModal } from '@/components/shared/TaskAssignmentModal';
import { useTaskAssignment } from '@/hooks/useTaskAssignment';
import { useTaskNavigation } from '@/hooks/useTaskNavigation';

interface TaskDetailsViewProps {
  taskId: string;
  users?: any[];
  onRefresh?: () => void;
}

export const TaskDetailsView: React.FC<TaskDetailsViewProps> = ({
  taskId,
  users = [],
  onRefresh,
}) => {
  const [taskDetails, setTaskDetails] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [isAssignModalOpen, setIsAssignModalOpen] = useState(false);

  const { assignTask, isAssigning } = useTaskAssignment({
    onSuccess: () => {
      loadTaskDetails();
      setIsAssignModalOpen(false);
      onRefresh?.();
    },
  });

  const { navigateToTaskView } = useTaskNavigation();

  const loadTaskDetails = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/tasks/${taskId}/navigation`);
      if (response.ok) {
        const data = await response.json();
        setTaskDetails(data);
      }
    } catch (error) {
      console.error('Error loading task details:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadTaskDetails();
  }, [taskId]);

  const handleAssignment = async (assignmentData: any) => {
    await assignTask(taskId, assignmentData);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { variant: 'outline' as const, label: 'Pending' },
      in_progress: { variant: 'default' as const, label: 'In Progress' },
      completed: { variant: 'success' as const, label: 'Completed' },
      cancelled: { variant: 'destructive' as const, label: 'Cancelled' },
      on_hold: { variant: 'secondary' as const, label: 'On Hold' },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const getPriorityBadge = (priority: string) => {
    const priorityConfig = {
      low: { variant: 'outline' as const, label: 'Low', color: 'text-green-600' },
      medium: { variant: 'secondary' as const, label: 'Medium', color: 'text-yellow-600' },
      high: { variant: 'warning' as const, label: 'High', color: 'text-orange-600' },
      urgent: { variant: 'destructive' as const, label: 'Urgent', color: 'text-red-600' },
    };

    const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig.medium;
    return (
      <div className="flex items-center gap-1">
        <AlertTriangle className={`h-3 w-3 ${config.color}`} />
        <Badge variant={config.variant}>{config.label}</Badge>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent" />
      </div>
    );
  }

  if (!taskDetails) {
    return (
      <div className="text-center p-8">
        <p className="text-gray-500">Task not found</p>
      </div>
    );
  }

  const { task, canNavigateToEntity } = taskDetails;

  return (
    <>
      <div className="space-y-6">
        {/* Task Header */}
        <Card>
          <CardHeader>
            <div className="flex items-start justify-between">
              <div>
                <CardTitle className="text-xl">{task.title}</CardTitle>
                <p className="text-sm text-gray-500 mt-1">Task #{task.task_number}</p>
              </div>
              <div className="flex items-center gap-2">
                {getStatusBadge(task.status)}
                {getPriorityBadge(task.priority)}
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-gray-700">{task.description}</p>
          </CardContent>
        </Card>

        {/* Entity Navigation */}
        {canNavigateToEntity && task.entity_type === 'application' && (
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Related Application
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 mb-4">
                This task is related to an application. Click below to open the evaluation interface.
              </p>
              <Button
                onClick={() => navigateToTaskView(taskId)}
                className="w-full"
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                Open Application Evaluation
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Task Details */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Task Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-gray-500">Task Type</p>
                <p className="text-sm capitalize">{task.task_type.replace('_', ' ')}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Created</p>
                <p className="text-sm">{format(new Date(task.created_at), 'PPP')}</p>
              </div>
              {task.due_date && (
                <div>
                  <p className="text-sm font-medium text-gray-500">Due Date</p>
                  <p className="text-sm flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    {format(new Date(task.due_date), 'PPP')}
                  </p>
                </div>
              )}
              {task.completed_at && (
                <div>
                  <p className="text-sm font-medium text-gray-500">Completed</p>
                  <p className="text-sm">{format(new Date(task.completed_at), 'PPP')}</p>
                </div>
              )}
            </div>

            {/* Assignment Information */}
            <Separator />
            <div>
              <p className="text-sm font-medium text-gray-500 mb-2">Assignment</p>
              {task.assignee ? (
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium">
                        {task.assignee.first_name} {task.assignee.last_name}
                      </p>
                      <p className="text-xs text-gray-500">{task.assignee.email}</p>
                      {task.assigned_at && (
                        <p className="text-xs text-gray-400">
                          Assigned {format(new Date(task.assigned_at), 'PPP')}
                        </p>
                      )}
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsAssignModalOpen(true)}
                  >
                    <UserPlus className="h-4 w-4 mr-1" />
                    Reassign
                  </Button>
                </div>
              ) : (
                <div className="flex items-center justify-between">
                  <p className="text-sm text-gray-500">Unassigned</p>
                  <Button
                    variant="default"
                    size="sm"
                    onClick={() => setIsAssignModalOpen(true)}
                  >
                    <UserPlus className="h-4 w-4 mr-1" />
                    Assign Task
                  </Button>
                </div>
              )}
            </div>

            {/* Notes */}
            {task.review_notes && (
              <>
                <Separator />
                <div>
                  <p className="text-sm font-medium text-gray-500 mb-2">Notes</p>
                  <p className="text-sm bg-gray-50 p-3 rounded-md">{task.review_notes}</p>
                </div>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Task Assignment Modal */}
      <TaskAssignmentModal
        isOpen={isAssignModalOpen}
        onClose={() => setIsAssignModalOpen(false)}
        onAssign={handleAssignment}
        task={task}
        users={users}
        loading={isAssigning}
      />
    </>
  );
};
