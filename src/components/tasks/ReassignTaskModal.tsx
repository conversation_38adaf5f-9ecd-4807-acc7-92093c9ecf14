'use client';

import React from 'react';
import { Task } from '../../services/task-assignment';
import AssignModal from '../common/AssignModal';

interface ReassignTaskModalProps {
  isOpen: boolean;
  onClose: () => void;
  task: Task | null;
  onReassignSuccess: () => void;
}

const ReassignTaskModal: React.FC<ReassignTaskModalProps> = ({
  isOpen,
  onClose,
  task,
  onReassignSuccess
}) => {
  return (
    <AssignModal
      isOpen={isOpen}
      onClose={onClose}
      itemId={task?.task_id || null}
      itemType="task"
      itemTitle={task?.title}
      mode="reassign"
      task={task}
      onReassignSuccess={onReassignSuccess}
    />
  );
};

export default ReassignTaskModal;
