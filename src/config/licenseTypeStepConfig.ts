/**
 * License Type Step Configuration System
 *
 * SINGLE SOURCE OF TRUTH for all license type step configurations
 *
 * This is the consolidated configuration system that defines:
 * - Which form steps are required for each license type
 * - Step order and navigation flow
 * - Validation requirements and estimated times
 * - Fallback configurations for unknown license types
 *
 * Supports the 5 specified license type codes:
 * - telecommunications
 * - postal_services
 * - standards_compliance
 * - broadcasting
 * - spectrum_management
 *
 * Features:
 * - Optimized step loading based on license type codes
 * - Automatic fallback for unsupported types
 * - Smart license type resolution (UUID, code, name mapping)
 * - Comprehensive helper functions for navigation and progress tracking
 */

export interface StepConfig {
  id: string;
  name: string;
  component: string;
  route: string;
  required: boolean;
  description: string;
  estimatedTime: string; // in minutes
}

export interface LicenseTypeStepConfig {
  licenseTypeId: string;
  name: string;
  description: string;
  steps: StepConfig[];
  estimatedTotalTime: string;
  requirements: string[];
}

// Base steps that can be used across license types
const BASE_STEPS: Record<string, StepConfig> = {
  applicantInfo: {
    id: 'applicant-info',
    name: 'Applicant Information',
    component: 'ApplicantInfo',
    route: 'applicant-info',
    required: true,
    description: 'Personal or company information of the applicant',
    estimatedTime: '5'
  },
  addressInfo: {
    id: 'address-info',
    name: 'Address Information',
    component: 'AddressInfo',
    route: 'address-info',
    required: true,
    description: 'Physical and postal address details',
    estimatedTime: '3'
  },
  contactInfo: {
    id: 'contact-info',
    name: 'Contact Information',
    component: 'ContactInfo',
    route: 'contact-info',
    required: true,
    description: 'Contact details and communication preferences',
    estimatedTime: '5'
  },

  management: {
    id: 'management',
    name: 'Management Structure',
    component: 'Management',
    route: 'management',
    required: false,
    description: 'Management team and organizational structure',
    estimatedTime: '8'
  },
  professionalServices: {
    id: 'professional-services',
    name: 'Professional Services',
    component: 'ProfessionalServices',
    route: 'professional-services',
    required: false,
    description: 'External consultants and service providers',
    estimatedTime: '6'
  },
  serviceScope: {
    id: 'service-scope',
    name: 'Service Scope',
    component: 'ServiceScope',
    route: 'service-scope',
    required: true,
    description: 'Services offered and geographic coverage',
    estimatedTime: '8'
  },

  legalHistory: {
    id: 'legal-history',
    name: 'Legal History',
    component: 'LegalHistory',
    route: 'legal-history',
    required: true,
    description: 'Legal compliance and regulatory history',
    estimatedTime: '5'
  },
  documents: {
    id: 'documents',
    name: 'Required Documents',
    component: 'Documents',
    route: 'documents',
    required: true,
    description: 'Upload required documents for license application',
    estimatedTime: '10'
  },
  submit: {
    id: 'submit',
    name: 'Submit Application',
    component: 'Submit',
    route: 'submit',
    required: true,
    description: 'Review and submit your application',
    estimatedTime: '5'
  }
};

// License type specific configurations
export const LICENSE_TYPE_STEP_CONFIGS: Record<string, LicenseTypeStepConfig> = {
  telecommunications: {
    licenseTypeId: 'telecommunications',
    name: 'Telecommunications License',
    description: 'License for telecommunications service providers including ISPs, mobile operators, and fixed-line services',
    steps: [
      BASE_STEPS.applicantInfo,
      BASE_STEPS.addressInfo,
      BASE_STEPS.contactInfo,
      BASE_STEPS.management,
      BASE_STEPS.serviceScope,
      BASE_STEPS.legalHistory,
      BASE_STEPS.documents,
      BASE_STEPS.submit
    ],
    estimatedTotalTime: '97 minutes',
    requirements: [
      'Business registration certificate',
      'Tax compliance certificate',
      'Technical specifications',
      'Financial statements',
      'Management CVs',
      'Network coverage plans'
    ]
  },

  postal_services: {
    licenseTypeId: 'postal_services',
    name: 'Postal Services License',
    description: 'License for postal and courier service providers',
    steps: [
      BASE_STEPS.applicantInfo,
      BASE_STEPS.addressInfo,
      BASE_STEPS.contactInfo,
      BASE_STEPS.legalHistory,
      BASE_STEPS.documents,
      BASE_STEPS.submit
    ],
    estimatedTotalTime: '65 minutes',
    requirements: [
      'Business registration certificate',
      'Fleet inventory',
      'Service coverage map',
      'Insurance certificates',
      'Premises documentation'
    ]
  },

  standards_compliance: {
    licenseTypeId: 'standards_compliance',
    name: 'Standards Compliance License',
    description: 'License for standards compliance and certification services',
    steps: [
      BASE_STEPS.applicantInfo,
      BASE_STEPS.addressInfo,
      BASE_STEPS.contactInfo,
      BASE_STEPS.management,
      BASE_STEPS.professionalServices,
      BASE_STEPS.serviceScope,
      BASE_STEPS.legalHistory,
      BASE_STEPS.documents,
      BASE_STEPS.submit
    ],
    estimatedTotalTime: '82 minutes',
    requirements: [
      'Accreditation certificates',
      'Technical competency proof',
      'Quality management system',
      'Laboratory facilities documentation',
      'Staff qualifications'
    ]
  },

  broadcasting: {
    licenseTypeId: 'broadcasting',
    name: 'Broadcasting License',
    description: 'License for radio and television broadcasting services',
    steps: [
      BASE_STEPS.applicantInfo,
      BASE_STEPS.addressInfo,
      BASE_STEPS.contactInfo,
      BASE_STEPS.management,
      BASE_STEPS.serviceScope,
      BASE_STEPS.professionalServices,
      BASE_STEPS.legalHistory,
      BASE_STEPS.documents,
      BASE_STEPS.submit
    ],
    estimatedTotalTime: '86 minutes',
    requirements: [
      'Broadcasting equipment specifications',
      'Content programming plan',
      'Studio facility documentation',
      'Transmission coverage maps',
      'Local content compliance plan'
    ]
  },

  spectrum_management: {
    licenseTypeId: 'spectrum_management',
    name: 'Spectrum Management License',
    description: 'License for radio frequency spectrum management and allocation',
    steps: [
      BASE_STEPS.applicantInfo,
      BASE_STEPS.management,
      BASE_STEPS.serviceScope,
      BASE_STEPS.professionalServices,
      BASE_STEPS.legalHistory,
      BASE_STEPS.documents,
      BASE_STEPS.submit
    ],
    estimatedTotalTime: '89 minutes',
    requirements: [
      'Spectrum usage plan',
      'Technical interference analysis',
      'Equipment type approval',
      'Frequency coordination agreements',
      'Monitoring capabilities documentation'
    ]
  },

  clf: {
    licenseTypeId: 'clf',
    name: 'CLF License',
    description: 'Consumer Lending and Finance license',
    steps: [
      BASE_STEPS.applicantInfo,
      BASE_STEPS.addressInfo,
      BASE_STEPS.contactInfo,
      BASE_STEPS.management,
      BASE_STEPS.professionalServices,
      BASE_STEPS.legalHistory,
      BASE_STEPS.documents,
      BASE_STEPS.submit
    ],
    estimatedTotalTime: '51 minutes',
    requirements: [
      'Financial institution license',
      'Capital adequacy documentation',
      'Risk management framework',
      'Consumer protection policies',
      'Anti-money laundering procedures'
    ]
  }
};

// License type name to config key mapping
const LICENSE_TYPE_NAME_MAPPING: Record<string, string> = {
  'telecommunications': 'telecommunications',
  'postal services': 'postal_services',
  'postal_services': 'postal_services',
  'standards compliance': 'standards_compliance',
  'standards_compliance': 'standards_compliance',
  'broadcasting': 'broadcasting',
  'spectrum management': 'spectrum_management',
  'spectrum_management': 'spectrum_management',
  'clf': 'clf',
  'consumer lending and finance': 'clf'
};

// Default fallback configuration for unknown license types
const DEFAULT_FALLBACK_CONFIG: LicenseTypeStepConfig = {
  licenseTypeId: 'default',
  name: 'Standard License Application',
  description: 'Standard license application process with all required steps',
  steps: [
    BASE_STEPS.applicantInfo,
    BASE_STEPS.addressInfo,
    BASE_STEPS.contactInfo,
    BASE_STEPS.management,
    BASE_STEPS.professionalServices,
    BASE_STEPS.serviceScope,
    BASE_STEPS.legalHistory,
    BASE_STEPS.documents,
    BASE_STEPS.submit
  ],
  estimatedTotalTime: '120 minutes',
  requirements: [
    'Business registration certificate',
    'Tax compliance certificate',
    'Financial statements',
    'Management CVs',
    'Professional qualifications',
    'Service documentation'
  ]
};

// Helper functions
export const getLicenseTypeStepConfig = (licenseTypeId: string): LicenseTypeStepConfig => {
  // Check if licenseTypeId is valid
  if (!licenseTypeId || typeof licenseTypeId !== 'string') {
    return DEFAULT_FALLBACK_CONFIG;
  }

  // First try direct lookup with exact match
  let config = LICENSE_TYPE_STEP_CONFIGS[licenseTypeId];
  if (config) {
    return config;
  }

  // Try normalized lookup (lowercase with underscores)
  const normalizedId = licenseTypeId.toLowerCase().replace(/[^a-z0-9]/g, '_');
  config = LICENSE_TYPE_STEP_CONFIGS[normalizedId];
  if (config) {
    return config;
  }

  // Try name mapping for common variations
  const mappedKey = LICENSE_TYPE_NAME_MAPPING[normalizedId];
  if (mappedKey) {
    return LICENSE_TYPE_STEP_CONFIGS[mappedKey];
  }

  // If licenseTypeId looks like a UUID, try to get the code from license types
  if (isUUID(licenseTypeId)) {
    const code = getLicenseTypeCodeFromUUID(licenseTypeId);
    if (code) {
      const foundConfig = LICENSE_TYPE_STEP_CONFIGS[code];
      if (foundConfig) {
        return foundConfig;
      }
    }
  }

  // Try partial matching for known license type codes
  const knownCodes = Object.keys(LICENSE_TYPE_STEP_CONFIGS);
  const partialMatch = knownCodes.find(code =>
    licenseTypeId.toLowerCase().includes(code) ||
    code.includes(licenseTypeId.toLowerCase())
  );

  if (partialMatch) {
    return LICENSE_TYPE_STEP_CONFIGS[partialMatch];
  }
  return DEFAULT_FALLBACK_CONFIG;
};

// Helper function to check if a string is a UUID
const isUUID = (str: string): boolean => {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(str);
};

// Helper function to get license type code from UUID
// This will be populated by the license type service
let licenseTypeUUIDToCodeMap: Record<string, string> = {};

export const setLicenseTypeUUIDToCodeMap = (map: Record<string, string>) => {
  licenseTypeUUIDToCodeMap = map;
};

const getLicenseTypeCodeFromUUID = (uuid: string): string | null => {
  return licenseTypeUUIDToCodeMap[uuid] || null;
};

// Optimized function to get steps by license type code
export const getStepsByLicenseTypeCode = (licenseTypeCode: string): StepConfig[] => {
  // Validate known license type codes
  const validCodes = ['telecommunications', 'postal_services', 'standards_compliance', 'broadcasting', 'spectrum_management'];

  if (validCodes.includes(licenseTypeCode)) {
    const config = LICENSE_TYPE_STEP_CONFIGS[licenseTypeCode];
    if (config) {
      return config.steps;
    }
  }
  return DEFAULT_FALLBACK_CONFIG.steps;
};

// Enhanced function to check if a license type code is supported
export const isLicenseTypeCodeSupported = (licenseTypeCode: string): boolean => {
  const validCodes = ['telecommunications', 'postal_services', 'standards_compliance', 'broadcasting', 'spectrum_management'];
  return validCodes.includes(licenseTypeCode);
};

// Function to get all supported license type codes
export const getSupportedLicenseTypeCodes = (): string[] => {
  return ['telecommunications', 'postal_services', 'standards_compliance', 'broadcasting', 'spectrum_management'];
};

export const getStepByRoute = (licenseTypeId: string, stepRoute: string): StepConfig | null => {
  const config = getLicenseTypeStepConfig(licenseTypeId);
  if (!config) return null;

  return config.steps.find(step => step.route === stepRoute) || null;
};

export const getStepByIndex = (licenseTypeId: string, stepIndex: number): StepConfig | null => {
  const config = getLicenseTypeStepConfig(licenseTypeId);
  if (stepIndex < 0 || stepIndex >= config.steps.length) return null;

  return config.steps[stepIndex];
};

export const getStepIndex = (licenseTypeId: string, stepRoute: string): number => {
  const config = getLicenseTypeStepConfig(licenseTypeId);
  return config.steps.findIndex(step => step.route === stepRoute);
};

export const getTotalSteps = (licenseTypeId: string): number => {
  const config = getLicenseTypeStepConfig(licenseTypeId);
  return config.steps.length;
};

export const getRequiredSteps = (licenseTypeId: string): StepConfig[] => {
  const config = getLicenseTypeStepConfig(licenseTypeId);
  return config.steps.filter(step => step.required);
};

export const getOptionalSteps = (licenseTypeId: string): StepConfig[] => {
  const config = getLicenseTypeStepConfig(licenseTypeId);
  return config.steps.filter(step => !step.required);
};

export const calculateProgress = (licenseTypeId: string, completedSteps: string[]): number => {
  const config = getLicenseTypeStepConfig(licenseTypeId);
  const totalSteps = config.steps.length;
  const completed = completedSteps.length;

  return Math.round((completed / totalSteps) * 100);
};

export const getNextStep = (licenseTypeId: string, currentStepRoute: string): StepConfig | null => {
  const config = getLicenseTypeStepConfig(licenseTypeId);
  const currentIndex = getStepIndex(licenseTypeId, currentStepRoute);

  if (currentIndex === -1 || currentIndex >= config.steps.length - 1) return null;

  return config.steps[currentIndex + 1];
};

export const getPreviousStep = (licenseTypeId: string, currentStepRoute: string): StepConfig | null => {
  const config = getLicenseTypeStepConfig(licenseTypeId);
  const currentIndex = getStepIndex(licenseTypeId, currentStepRoute);

  if (currentIndex <= 0) return null;

  return config.steps[currentIndex - 1];
};

// Enhanced function to get step configuration with license type code validation
export const getOptimizedStepConfig = (licenseTypeCode: string): LicenseTypeStepConfig => {
  // Check if it's a supported license type code
  if (isLicenseTypeCodeSupported(licenseTypeCode)) {
    const config = LICENSE_TYPE_STEP_CONFIGS[licenseTypeCode];
    return config;
  }
  return DEFAULT_FALLBACK_CONFIG;
};
