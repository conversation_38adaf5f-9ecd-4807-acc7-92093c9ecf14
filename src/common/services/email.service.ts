import { Injectable, Logger } from '@nestjs/common';
import { MailerService } from '@nestjs-modules/mailer';
import { join } from 'path';
import { assetsDir } from '../../app.module';
import { AuthConstants, AuthUtils } from '../constants/auth.constants';

export interface EmailContext {
  [key: string]: any;
}

export interface EmailAttachment {
  filename: string;
  path: string;
  cid: string;
}

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);

  constructor(private readonly mailerService: MailerService) {}

  /**
   * Send email with standard MACRA logo attachment
   */
  async sendEmail(
    to: string,
    template: string,
    subject: string,
    context: EmailContext,
    additionalAttachments: EmailAttachment[] = []
  ): Promise<void> {
    try {
      const attachments = [
        {
          filename: AuthConstants.EMAIL_ATTACHMENT.LOGO_FILENAME,
          path: join(assetsDir, AuthConstants.EMAIL_ATTACHMENT.LOGO_FILENAME),
          cid: AuthConstants.EMAIL_ATTACHMENT.LOGO_CID,
        },
        ...additionalAttachments,
      ];

      await this.mailerService.sendMail({
        to,
        subject,
        template,
        context: {
          ...context,
          year: AuthUtils.getCurrentYear(),
        },
        attachments,
      });

      this.logger.log(`Email sent successfully to ${to} with template ${template}`);
    } catch (error) {
      this.logger.error(`Failed to send email to ${to} with template ${template}:`, error);
      throw error;
    }
  }

  /**
   * Send 2FA verification email
   */
  async send2FAEmail(
    to: string,
    template: string,
    subject: string,
    context: {
      name: string;
      message: string;
      verifyUrl: string;
    }
  ): Promise<void> {
    return this.sendEmail(to, template, subject, context);
  }

  /**
   * Send login alert email
   */
  async sendLoginAlertEmail(
    to: string,
    subject: string,
    context: {
      userName: string;
      loginUrl: string;
      ip: string;
      country: string;
      city: string;
      userAgent: string;
      message: string;
    }
  ): Promise<void> {
    return this.sendEmail(to, 'login-alert', subject, context);
  }

  /**
   * Send password reset confirmation email
   */
  async sendPasswordResetEmail(
    to: string,
    context: {
      userName: string;
      loginUrl: string;
    }
  ): Promise<void> {
    return this.sendEmail(
      to,
      'reset',
      'Password Reset - MACRA Digital Portal',
      context
    );
  }

  /**
   * Send email verification after registering.
   */
  async sendVerifyEmail(
    to: string,
    context: {
      userName: string;
      verifyUrl: string;
    }
  ): Promise<void> {
    return this.sendEmail(
      to,
      '2fa',
      'Verify Email - MACRA Digital Portal',
      context
    );
  }

  /**
   * Create verification URL for 2FA
   */
  static createVerificationUrl(
    userId: string,
    secret: string,
    token: string,
    action: string,
    roles: Array<{ name: string }> | undefined
  ): string {
    const urlPrefix = AuthUtils.getUrlPrefix(roles);
    const urlRedirect = AuthUtils.getRedirectUrl(action as any);
    
    return `${process.env.FRONTEND_URL}/${urlPrefix}/${urlRedirect}?i=${encodeURIComponent(userId)}&unique=${encodeURIComponent(secret)}&c=${encodeURIComponent(token)}`;
  }

  static createVerifyEmailUrl(
    token: string,
    email: string,
    roles: Array<{ name: string }> | undefined
    ): string {
      const urlPrefix = AuthUtils.getUrlPrefix(roles);
      return `${process.env.FRONTEND_URL}/${urlPrefix}/auth/verify-email?em=${encodeURIComponent(email)}&tn=${encodeURIComponent(token)}`;
  }
}
