/**
 * Authentication and authorization constants
 */

export enum TwoFactorAction {
  LOGIN = 'login',
  RESET = 'reset',
  VERIFY = 'verify'
}

export const AuthConstants = {
  // Staff roles for determining user privileges
  STAFF_ROLES: ['admin', 'administrator', 'staff', 'moderator', 'manager'] as const,
  
  // Password hashing rounds
  PASSWORD_HASH_ROUNDS: 12,
  
  // Two-factor authentication settings
  TWO_FACTOR: {
    SECRET_LENGTH: 16,
    TOKEN_EXPIRY_MINUTES: 5,
    CODE_HASH_ROUNDS: 8,
    ISSUER_NAME: 'MACRA Digital Portal',
    ONE_HOUR_MS: 60 * 60 * 1000,
  },
  
  // URL patterns
  URL_PATTERNS: {
    CUSTOMER_PREFIX: 'customer/auth',
    STAFF_PREFIX: 'auth',
    RESET_REDIRECT: 'reset-password',
    VERIFY_REDIRECT: 'verify-2fa',
    EMAIL_VERIFY_REDIRECT: 'verify-email',
    LOGIN_PATH: '/auth/login',
  },
  
  // Email attachment settings
  EMAIL_ATTACHMENT: {
    LOGO_FILENAME: 'macra-logo.png',
    LOGO_CID: 'logo@macra',
  },
} as const;

export const EmailTemplates = {
  TWO_FACTOR: '2fa',
  LOGIN_ALERT: 'login-alert',
  RESET: 'reset',
} as const;

export const EmailSubjects = {
  VERIFY_OTP: 'Verify OTP - MACRA Digital Portal',
  PASSWORD_RESET: 'Password Reset - MACRA Digital Portal',
  LOGIN_DETAILS: 'Login Details - MACRA Digital Portal',
  TWO_FACTOR_SETUP: 'Two-Factor Authentication Setup - MACRA Digital Portal',
} as const;

export const TwoFactorMessages = {
  [TwoFactorAction.RESET]: 'You are receiving this email because we received a password reset request for your account.',
  [TwoFactorAction.LOGIN]: 'You are receiving this email because we received a login request for your account.',
  [TwoFactorAction.VERIFY]: 'Thank you for registering with MACRA Digital Portal. Please click the link below to verify your email address and activate your account.',
} as const;

export const AuthMessages = {
  INVALID_CREDENTIALS: 'Invalid email or password',
  ACCOUNT_INACTIVE: 'Account is not active',
  USER_NOT_FOUND: 'User not found',
  INVALID_VERIFICATION_LINK: 'Invalid verification link!',
  EXPIRED_VERIFICATION_LINK: 'Expired verification link!',
  INVALID_VERIFICATION_CODE: 'Invalid verification code',
  INVALID_RESET_CODE: 'Invalid reset code',
  PASSWORD_SAME_AS_CURRENT: 'New password cannot be the same as the current password',
  TWO_FACTOR_ALREADY_ENABLED: 'Two-factor authentication is already enabled for {email}.\n\n Redirecting to login..',
  TWO_FACTOR_CODE_SENT: '2FA code has been sent',
  PASSWORD_RESET_SUCCESS: 'Password reset successfully for {email}! Please login with your new password.',
  TWO_FACTOR_SETUP_SUCCESS: 'Two factor authentication initiation for {email} successful! Please check your email for the verification link.',
  OTP_VERIFIED: 'OTP verified successfully',
  TWO_FACTOR_ENABLED: 'Two-factor authentication enabled for {email}!',
  PASSWORD_RESET_EMAIL_SENT: 'If the email exists, a password reset link has been sent.',
  LOGIN_NOTIFICATION_MESSAGE: 'We detected a new login to your MACRA Digital Portal account. If this was you, no action is needed.',
  TWO_FACTOR_ENABLED_MESSAGE: 'Two-factor authentication has been successfully enabled for your account. Please login to access your account',
} as const;

/**
 * Utility functions for authentication
 */
export class AuthUtils {
  /**
   * Check if user has staff privileges based on roles
   */
  static isStaffUser(roles: Array<{ name: string }> | undefined): boolean {
    return roles?.some(role =>
      (AuthConstants.STAFF_ROLES as readonly string[]).includes(role.name.toLowerCase())
    ) ?? false;
  }

  /**
   * Check if user is a customer
   */
  static isCustomerUser(roles: Array<{ name: string }> | undefined): boolean {
    return roles?.some(role => role.name.toLowerCase() === 'customer') ?? false;
  }

  /**
   * Get URL prefix based on user roles
   */
  static getUrlPrefix(roles: Array<{ name: string }> | undefined): string {
    return AuthUtils.isCustomerUser(roles) 
      ? AuthConstants.URL_PATTERNS.CUSTOMER_PREFIX 
      : AuthConstants.URL_PATTERNS.STAFF_PREFIX;
  }

  /**
   * Get redirect URL based on action
   */
  static getRedirectUrl(action: TwoFactorAction): string {
    switch (action) {
      case TwoFactorAction.RESET:
        return AuthConstants.URL_PATTERNS.RESET_REDIRECT;
      case TwoFactorAction.VERIFY:
        return AuthConstants.URL_PATTERNS.EMAIL_VERIFY_REDIRECT;
      case TwoFactorAction.LOGIN:
      default:
        return AuthConstants.URL_PATTERNS.VERIFY_REDIRECT;
    }
  }

  /**
   * Format message with placeholders
   */
  static formatMessage(template: string, replacements: Record<string, string>): string {
    return Object.entries(replacements).reduce(
      (message, [key, value]) => message.replace(`{${key}}`, value),
      template
    );
  }

  /**
   * Get current year for email templates
   */
  static getCurrentYear(): number {
    return new Date().getFullYear();
  }

  /**
   * Create expiry date for tokens
   */
  static createExpiryDate(minutes: number = AuthConstants.TWO_FACTOR.TOKEN_EXPIRY_MINUTES): Date {
    return new Date(Date.now() + minutes * 60 * 1000);
  }

  /**
   * Check if 2FA is required based on last login
   */
  static requires2FA(twoFactorEnabled: boolean, lastLogin: Date | null | undefined): boolean {
    if (!twoFactorEnabled) return false;

    const lastLoginTime = lastLogin ? new Date(lastLogin).getTime() : 0;
    const oneHourAgo = Date.now() - AuthConstants.TWO_FACTOR.ONE_HOUR_MS;

    return lastLoginTime < oneHourAgo;
  }
}
