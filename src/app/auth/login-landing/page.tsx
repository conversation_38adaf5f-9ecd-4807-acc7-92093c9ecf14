'use client';

import { Suspense } from 'react';
import Link from 'next/link';
import Image from 'next/image';

function LoginLandingForm() {
  return (
    <>
      <style jsx>{`
        @keyframes float {
          0%, 100% {
            transform: translateY(0px);
          }
          50% {
            transform: translateY(-10px);
          }
        }
        
        .floating-header {
          animation: float 3s ease-in-out infinite;
        }
      `}</style>

      <div className="min-h-screen bg-gray-50 font-inter overflow-x-hidden overflow-y-auto">
      {/* Top Header Bar */}
      <div className="bg-white py-4 border-b border-gray-200 shadow-sm">
        <div className="max-w-6xl mx-auto px-5 flex justify-between items-center">
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-white rounded-full flex items-center justify-center shadow-md p-1">
              <Image 
                src="/images/macra-logo.png" 
                alt="MACRA Logo" 
                width={40} 
                height={40}
                className="max-w-full max-h-full object-contain" 
              />
            </div>
            <div className="flex flex-col">
              <h1 className="text-2xl font-bold text-red-600 m-0 leading-none">MACRA</h1>
              <p className="text-xs text-red-600 m-0 font-medium">Digital Portal</p>
            </div>
          </div>
          <div className="flex gap-2.5">
            <Link
              href="/auth/login"
              className="px-4 py-2 bg-transparent text-gray-700 border border-gray-300 rounded-lg text-sm font-semibold cursor-pointer transition-all duration-300 no-underline inline-block text-center hover:bg-gray-50 hover:border-gray-400"
            >
              Staff Login
            </Link>
            <Link
              href="/customer/auth/login-landing"
              className="px-4 py-2 bg-primary text-white border-none rounded-lg text-sm font-semibold cursor-pointer transition-all duration-300 no-underline inline-block text-center hover:bg-red-600 hover:-translate-y-0.5 hover:shadow-lg"
            >
              Customer Portal
            </Link>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex min-h-[calc(100vh-80px)] items-center justify-center px-5 py-10">
        <div className="max-w-6xl w-full">
          {/* Services Section - Centered */}
          <div className="text-center text-gray-700">
            <h3 className="floating-header text-3xl font-bold text-red-600 text-center my-8 relative after:content-[''] after:absolute after:-bottom-2 after:left-1/2 after:transform after:-translate-x-1/2 after:w-20 after:h-0.5 after:bg-red-600 after:rounded-sm">
              Staff Portal
            </h3>
            <p className="text-base leading-relaxed text-gray-500 max-w-3xl mx-auto mb-10 text-center">
              Administrative access for MACRA staff to manage licensing, applications, and regulatory services across all departments.
            </p>
            
            {/* Service Categories */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-5xl mx-auto">
              <div className="bg-white rounded-xl p-6 text-center shadow-md border border-gray-200 transition-all duration-300 cursor-pointer min-h-[160px] flex flex-col justify-center hover:-translate-y-1 hover:shadow-lg hover:border-red-600 group">
                <div className="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-4 text-white text-2xl group-hover:bg-red-700 group-hover:shadow-lg transition-all duration-300">
                  <i className="fas fa-mail-bulk"></i>
                </div>
                <h4 className="text-lg font-semibold text-red-600 mb-2">Postal Services</h4>
                <p className="text-sm text-gray-500 m-0 leading-snug">Manage Courier and Posts License applications, review submissions, and process approvals.</p>
              </div>
              
              <div className="bg-white rounded-xl p-6 text-center shadow-md border border-gray-200 transition-all duration-300 cursor-pointer min-h-[160px] flex flex-col justify-center hover:-translate-y-1 hover:shadow-lg hover:border-red-600 group">
                <div className="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-4 text-white text-2xl group-hover:bg-red-700 group-hover:shadow-lg transition-all duration-300">
                  <i className="fas fa-broadcast-tower"></i>
                </div>
                <h4 className="text-lg font-semibold text-red-600 mb-2">Telecommunications</h4>
                <p className="text-sm text-gray-500 m-0 leading-snug">Review and process Telecommunications License applications, manage spectrum allocations, and oversee compliance.</p>
              </div>
              
              <div className="bg-white rounded-xl p-6 text-center shadow-md border border-gray-200 transition-all duration-300 cursor-pointer min-h-[160px] flex flex-col justify-center hover:-translate-y-1 hover:shadow-lg hover:border-red-600 group">
                <div className="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-4 text-white text-2xl group-hover:bg-red-700 group-hover:shadow-lg transition-all duration-300">
                  <i className="fas fa-cogs"></i>
                </div>
                <h4 className="text-lg font-semibold text-red-600 mb-2">Standards</h4>
                <p className="text-sm text-gray-500 m-0 leading-snug">Apply for Standards Licenses, including Type Approval Certificates and Short Code authorizations.</p>
              </div>
              
              <div className="bg-white rounded-xl p-6 text-center shadow-md border border-gray-200 transition-all duration-300 cursor-pointer min-h-[160px] flex flex-col justify-center hover:-translate-y-1 hover:shadow-lg hover:border-red-600 group">
                <div className="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-4 text-white text-2xl group-hover:bg-red-700 group-hover:shadow-lg transition-all duration-300">
                  <i className="fas fa-shield-alt"></i>
                </div>
                <h4 className="text-lg font-semibold text-red-600 mb-2">Converged Licensing Framework</h4>
                <p className="text-sm text-gray-500 m-0 leading-snug">Apply for CLF licenses to operate telecom infrastructure, offer network or application services (e.g. ISPs, MVNOs), and deliver content services like broadcasting and digital media.</p>
              </div>
              
              <div className="bg-white rounded-xl p-6 text-center shadow-md border border-gray-200 transition-all duration-300 cursor-pointer min-h-[160px] flex flex-col justify-center hover:-translate-y-1 hover:shadow-lg hover:border-red-600 group">
                <div className="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-4 text-white text-2xl group-hover:bg-red-700 group-hover:shadow-lg transition-all duration-300">
                  <i className="fas fa-file-contract"></i>
                </div>
                <h4 className="text-lg font-semibold text-red-600 mb-2">Procurement</h4>
                <p className="text-sm text-gray-500 m-0 leading-snug">Submit bids for goods and service contracts and track the status of your applications.</p>
              </div>
              
              <div className="bg-white rounded-xl p-6 text-center shadow-md border border-gray-200 transition-all duration-300 cursor-pointer min-h-[160px] flex flex-col justify-center hover:-translate-y-1 hover:shadow-lg hover:border-red-600 group">
                <div className="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-4 text-white text-2xl group-hover:bg-red-700 group-hover:shadow-lg transition-all duration-300">
                  <i className="fas fa-users"></i>
                </div>
                <h4 className="text-lg font-semibold text-red-600 mb-2">Consumer Affairs</h4>
                <p className="text-sm text-gray-500 m-0 leading-snug">Access Consumer Affairs services to lodge complaints, resolve telecom-related issues, and stay informed about your rights and responsibilities.</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="bg-white border-t border-gray-200 py-6">
        <div className="max-w-6xl mx-auto px-5 text-center">
          <p className="text-gray-500 text-sm">
            © 2024 MACRA Digital Portal. All rights reserved.
          </p>
        </div>
      </div>
    </div>
    </>
  );
}

export default function LoginLandingPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <LoginLandingForm />
    </Suspense>
  );
}