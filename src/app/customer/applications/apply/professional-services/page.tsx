'use client';

import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import CustomerLayout from '@/components/customer/CustomerLayout';
import ApplicationLayout from '@/components/applications/ApplicationLayout';
import { useAuth } from '@/contexts/AuthContext';
import { TextArea } from '@/components/forms';
import { FormMessages } from '@/components/forms/FormMessages';
import { useDynamicNavigation } from '@/hooks/useDynamicNavigation';
import { professionalServicesService } from '@/services/professionalServicesService';

const ProfessionalServicesPage: React.FC = () => {
  const searchParams = useSearchParams();
  const { isAuthenticated, loading: authLoading } = useAuth();

  // Get query parameters
  const licenseCategoryId = searchParams.get('license_category_id');
  const applicationId = searchParams.get('application_id');

  // State management
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Dynamic navigation hook
  const {
    handleNext: dynamicHandleNext,
    handlePrevious: dynamicHandlePrevious
  } = useDynamicNavigation({
    currentStepRoute: 'professional-services',
    licenseCategoryId,
    applicationId
  });



  // Form data state
  const [formData, setFormData] = useState({
    consultants: '',
    service_providers: '',
    technical_support: '',
    maintenance_arrangements: '',
    professional_partnerships: '',
    outsourced_services: '',
    quality_assurance: '',
    training_programs: ''
  });

  // Form handling functions
  const handleFormChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Clear success message when user starts making changes
    if (successMessage) {
      setSuccessMessage(null);
    }

    // Clear validation error for this field
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  // Load existing data
  useEffect(() => {
    const loadData = async () => {
      if (!applicationId || !isAuthenticated || authLoading) return;

      try {
        setIsLoading(true);
        setError(null);

        // Load existing professional services data
        try {
          const existingData = await professionalServicesService.getProfessionalServicesByApplication(applicationId);
          if (existingData) {
            setFormData({
              consultants: existingData.consultants || '',
              service_providers: existingData.service_providers || '',
              technical_support: existingData.technical_support || '',
              maintenance_arrangements: existingData.maintenance_arrangements || '',
              professional_partnerships: existingData.professional_partnerships || '',
              outsourced_services: existingData.outsourced_services || '',
              quality_assurance: existingData.quality_assurance || '',
              training_programs: existingData.training_programs || ''
            });
            console.log('Professional services data auto-populated');
          } else {
            console.log('No existing professional services data found');
          }
        } catch (professionalServicesError: any) {
          console.error('Error loading professional services data:', professionalServicesError);
          // Silently handle error - form will start empty
        }

      } catch (error: any) {
        console.error('Error loading professional services form:', error);
        setError('Failed to load professional services form. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [applicationId, isAuthenticated, authLoading]);

  // Save function - following other apply pages pattern
  const handleSave = async (): Promise<boolean> => {
    if (!applicationId) {
      setValidationErrors({ save: 'Application ID is required' });
      return false;
    }

    setIsSaving(true);
    try {
      // Basic validation - check required fields
      const errors: Record<string, string> = {};

      if (!formData.consultants.trim()) errors.consultants = 'Consultants information is required';
      if (!formData.service_providers.trim()) errors.service_providers = 'Service providers information is required';
      if (!formData.technical_support.trim()) errors.technical_support = 'Technical support information is required';
      if (!formData.maintenance_arrangements.trim()) errors.maintenance_arrangements = 'Maintenance arrangements information is required';

      if (Object.keys(errors).length > 0) {
        setValidationErrors(errors);
        setIsSaving(false);
        return false;
      }

      // Create or update professional services record using the proper API
      const professionalServicesData = {
        consultants: formData.consultants,
        service_providers: formData.service_providers,
        technical_support: formData.technical_support,
        maintenance_arrangements: formData.maintenance_arrangements,
        professional_partnerships: formData.professional_partnerships,
        outsourced_services: formData.outsourced_services,
        quality_assurance: formData.quality_assurance,
        training_programs: formData.training_programs
      };

      // Save professional services data
      try {
        await professionalServicesService.createOrUpdateProfessionalServices(applicationId, professionalServicesData);
      } catch (saveError: any) {
        console.error('Error saving professional services data:', saveError);
        throw new Error('Failed to save professional services information');
      }

      setValidationErrors({});
      setSuccessMessage('Professional services information saved successfully!');

      // Auto-hide success message after 5 seconds
      setTimeout(() => {
        setSuccessMessage(null);
      }, 5000);

      console.log('Professional services information saved successfully');
      return true;

    } catch (error: any) {
      console.error('Error saving professional services information:', error);
      
      // Extract specific error message from API response
      let errorMessage = 'Failed to save professional services information. Please try again.';
      
      if (error.response?.data?.message) {
        // Use the specific error message from the backend
        errorMessage = error.response.data.message;
      } else if (error.message) {
        // Fallback to error message
        errorMessage = error.message;
      }
      
      setValidationErrors({ save: errorMessage });
      return false;
    } finally {
      setIsSaving(false);
    }
  };

  // Navigation functions using dynamic navigation
  const handleNext = async () => {
    await dynamicHandleNext(handleSave);
  };

  const handlePrevious = () => {
    dynamicHandlePrevious();
  };

  if (authLoading || isLoading) {
    return (
      <CustomerLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading professional services form...</p>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  if (error) {
    return (
      <CustomerLayout>
        <div className="max-w-4xl mx-auto p-6">
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
            <div className="flex items-center">
              <i className="ri-error-warning-line text-red-600 dark:text-red-400 text-2xl mr-4"></i>
              <div>
                <h3 className="text-lg font-medium text-red-800 dark:text-red-200">Error Loading Form</h3>
                <p className="text-red-700 dark:text-red-300 mt-1">{error}</p>
                <button
                  onClick={() => window.location.reload()}
                  className="mt-4 inline-flex items-center px-4 py-2 border border-red-300 dark:border-red-600 rounded-md text-sm font-medium text-red-700 dark:text-red-300 bg-white dark:bg-red-900/20 hover:bg-red-50 dark:hover:bg-red-900/40"
                >
                  <i className="ri-refresh-line mr-2"></i>
                  Retry
                </button>
              </div>
            </div>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  return (
    <CustomerLayout>
      <ApplicationLayout
        onNext={handleNext}
        onPrevious={handlePrevious}
        onSave={handleSave}
        showNextButton={true}
        showPreviousButton={true}
        showSaveButton={true}
        nextButtonText="Continue to Next Step"
        previousButtonText="Back to Previous Step"
        saveButtonText="Save Professional Services Information"
        nextButtonDisabled={false}
        previousButtonDisabled={false}
        saveButtonDisabled={isSaving}
      >
        <div className="max-w-4xl mx-auto">
          <FormMessages
            successMessage={successMessage}
            errorMessage={validationErrors.save}
          />

        {/* Header */}
        <div className="mb-6">
          <h2 className="text-lg font-medium text-gray-900 dark:text-gray-100">
            Professional Services Information
          </h2>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Provide details about professional services, consultants, and technical support arrangements.
          </p>
        </div>

          <div className="bg-white shadow-sm rounded-lg p-6">
            <div className="space-y-6">

            <div className="space-y-6">
              {/* Consultants */}
              <div>
                <TextArea
                  label="Consultants *"
                  name="consultants"
                  value={formData.consultants}
                  onChange={(e) => handleFormChange('consultants', e.target.value)}
                  placeholder="Describe the consultants and advisory services you use..."
                  rows={3}
                  error={validationErrors.consultants}
                />
              </div>

              {/* Service Providers */}
              <div>
                <TextArea
                  label="Service Providers *"
                  name="service_providers"
                  value={formData.service_providers}
                  onChange={(e) => handleFormChange('service_providers', e.target.value)}
                  placeholder="List your key service providers and their roles..."
                  rows={3}
                  error={validationErrors.service_providers}
                />
              </div>

              {/* Technical Support */}
              <div>
                <TextArea
                  label="Technical Support *"
                  name="technical_support"
                  value={formData.technical_support}
                  onChange={(e) => handleFormChange('technical_support', e.target.value)}
                  placeholder="Describe your technical support arrangements and capabilities..."
                  rows={3}
                  error={validationErrors.technical_support}
                />
              </div>

              {/* Maintenance Arrangements */}
              <div>
                <TextArea
                  label="Maintenance Arrangements *"
                  name="maintenance_arrangements"
                  value={formData.maintenance_arrangements}
                  onChange={(e) => handleFormChange('maintenance_arrangements', e.target.value)}
                  placeholder="Detail your maintenance and support arrangements..."
                  rows={3}
                  error={validationErrors.maintenance_arrangements}
                />
              </div>

              {/* Professional Partnerships */}
              <div>
                <TextArea
                  label="Professional Partnerships"
                  name="professional_partnerships"
                  value={formData.professional_partnerships}
                  onChange={(e) => handleFormChange('professional_partnerships', e.target.value)}
                  placeholder="Describe any professional partnerships and collaborations..."
                  rows={3}
                  error={validationErrors.professional_partnerships}
                />
              </div>

              {/* Outsourced Services */}
              <div>
                <TextArea
                  label="Outsourced Services"
                  name="outsourced_services"
                  value={formData.outsourced_services}
                  onChange={(e) => handleFormChange('outsourced_services', e.target.value)}
                  placeholder="List any services that are outsourced and the arrangements in place..."
                  rows={3}
                  error={validationErrors.outsourced_services}
                />
              </div>

              {/* Quality Assurance */}
              <div>
                <TextArea
                  label="Quality Assurance"
                  name="quality_assurance"
                  value={formData.quality_assurance}
                  onChange={(e) => handleFormChange('quality_assurance', e.target.value)}
                  placeholder="Describe your quality assurance processes and standards..."
                  rows={3}
                  error={validationErrors.quality_assurance}
                />
              </div>

              {/* Training Programs */}
              <div>
                <TextArea
                  label="Training Programs"
                  name="training_programs"
                  value={formData.training_programs}
                  onChange={(e) => handleFormChange('training_programs', e.target.value)}
                  placeholder="Describe any training programs for staff and professional development initiatives..."
                  rows={3}
                  error={validationErrors.training_programs}
                />
              </div>
            </div>
          </div>
      </ApplicationLayout>
    </CustomerLayout>
  );
};

export default ProfessionalServicesPage;
