'use client';

import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import CustomerLayout from '@/components/customer/CustomerLayout';
import ApplicationLayout from '@/components/applications/ApplicationLayout';
import { useAuth } from '@/contexts/AuthContext';
import FileUpload from '@/components/forms/FileUpload';
import { applicationService } from '@/services/applicationService';
import { documentService, LicenseCategoryDocumentData, DocumentData } from '@/services/documentService';
import { licenseCategoryDocumentService } from '@/services/licenseCategoryDocumentService';
import { useDynamicNavigation } from '@/hooks/useDynamicNavigation';

const DocumentsPage: React.FC = () => {
  const searchParams = useSearchParams();
  const { isAuthenticated, loading: authLoading } = useAuth();

  // URL parameters
  const licenseCategoryId = searchParams.get('license_category_id');
  const applicationId = searchParams.get('application_id');

  // State management
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [loadingWarning, setLoadingWarning] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  // Document data
  const [requiredDocuments, setRequiredDocuments] = useState<LicenseCategoryDocumentData[]>([]);
  const [uploadedDocuments, setUploadedDocuments] = useState<DocumentData[]>([]);
  const [documentFiles, setDocumentFiles] = useState<Record<string, File>>({});
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({});

  // Dynamic navigation hook
  const {
    handleNext: dynamicHandleNext,
    handlePrevious: dynamicHandlePrevious,
    nextStep
  } = useDynamicNavigation({
    currentStepRoute: 'documents',
    licenseCategoryId,
    applicationId
  });

  // Load required documents and existing uploads
  useEffect(() => {
    const loadData = async () => {
      if (!applicationId || !licenseCategoryId || !isAuthenticated || authLoading) return;

      try {
        setIsLoading(true);
        setError(null);
        setLoadingWarning(null);
        let requiredDocsArray: LicenseCategoryDocumentData[] = [];

        try {
          const requiredDocs = await licenseCategoryDocumentService.getLicenseCategoryDocumentsByCategory(licenseCategoryId);

          // Ensure requiredDocs is always an array
          requiredDocsArray = Array.isArray(requiredDocs) ? requiredDocs : [];
          setRequiredDocuments(requiredDocsArray);
        } catch (docError: any) {
          // Check if it's a 404 (no documents found) vs other errors
          if (docError.response?.status === 404) {
            requiredDocsArray = [];
            setRequiredDocuments([]);
            setLoadingWarning('No required documents configured for this license category.');
          } else {
            // Fallback: Use default required documents for common license types
            const defaultRequiredDocs = [
              {
                license_category_document_id: 'default_1',
                license_category_id: licenseCategoryId,
                name: 'Certificate of Incorporation',
                is_required: true
              },
              {
                license_category_document_id: 'default_2',
                license_category_id: licenseCategoryId,
                name: 'Business Plan',
                is_required: true
              },
              {
                license_category_document_id: 'default_3',
                license_category_id: licenseCategoryId,
                name: 'Financial Statements',
                is_required: true
              },
              {
                license_category_document_id: 'default_4',
                license_category_id: licenseCategoryId,
                name: 'Tax Clearance Certificate',
                is_required: false
              }
            ];
            requiredDocsArray = defaultRequiredDocs;
            setRequiredDocuments(defaultRequiredDocs);
            setLoadingWarning('Using default document requirements. Backend service not available.');
            console.log('📋 Using default required documents');
          }
        }

        try {
          // Use the existing /documents/by-application/{applicationId} endpoint
          const data = await documentService.getDocumentsByApplication(applicationId);
          const uploadedDocs = data.data

          const uploadedDocsArray = Array.isArray(uploadedDocs) ? uploadedDocs : [];
          setUploadedDocuments(uploadedDocsArray);
          // Cross-reference uploaded documents with required documents
          if (requiredDocsArray.length > 0) {
            const requiredDocTypes = requiredDocsArray.map(doc => doc.name.toLowerCase().replace(/\s+/g, '_'));
            const uploadedDocTypes = uploadedDocsArray.map(doc => doc.document_type);
            const missingDocs = requiredDocTypes.filter(type => !uploadedDocTypes.includes(type));
            const extraDocs = uploadedDocTypes.filter(type => !requiredDocTypes.includes(type));
            console.log('❌ Missing documents:', missingDocs);
            console.log('➕ Extra documents:', extraDocs);
          }

          // If no documents found, that's expected for new applications
          if (uploadedDocsArray.length === 0) {
            console.log('📝 No existing documents found - this is normal for new applications');
          }
        } catch (uploadError: any) {
          console.error('❌ Error loading uploaded documents:', uploadError);
          console.error('Error details:', {
            status: uploadError.response?.status,
            statusText: uploadError.response?.statusText,
            data: uploadError.response?.data,
            message: uploadError.message
          });

          // Set empty array for uploaded documents
          setUploadedDocuments([]);

          // Set appropriate warning based on error type
          if (uploadError.response?.status === 401) {
            setLoadingWarning('Authentication required. Please log in to view documents.');
          } else if (uploadError.response?.status === 404) {
            console.log('📝 No documents found for this application (404) - this is normal for new applications');
          } else {
            setLoadingWarning('Could not load existing documents from server. You can still upload new documents.');
          }
        }

      } catch (err: any) {
        setError('Failed to load documents data');
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [applicationId, licenseCategoryId, isAuthenticated, authLoading]);

  // Handle file selection
  const handleFileChange = (documentType: string, file: File | null) => {
    setDocumentFiles(prev => {
      const newFiles = { ...prev };
      if (file) {
        newFiles[documentType] = file;
      } else {
        delete newFiles[documentType];
      }
      return newFiles;
    });

    // Clear validation error for this document
    if (validationErrors[documentType]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[documentType];
        return newErrors;
      });
    }
  };

  // Upload a single document
  const uploadDocument = async (documentType: string, file: File): Promise<boolean> => {
    try {
      setUploadProgress(prev => ({ ...prev, [documentType]: 0 }));

      // Map the document name to the correct DocumentType enum value
      const requiredDoc = Array.isArray(requiredDocuments) ? requiredDocuments.find(doc =>
        doc.name.toLowerCase().replace(/\s+/g, '_') === documentType
      ) : undefined;
      const mappedDocumentType = requiredDoc ?
        documentService.mapDocumentNameToType(requiredDoc.name) :
        documentType;

      const uploadData = {
        document_type: mappedDocumentType,
        entity_type: 'application',
        entity_id: applicationId!,
        is_required: requiredDoc?.is_required || false
      };

      try {
        // Try to upload to backend service
        const result = await documentService.uploadDocument(file, uploadData);

        setUploadProgress(prev => ({ ...prev, [documentType]: 100 }));

        // Update uploaded documents list
        setUploadedDocuments(prev => [...prev, result.document]);
      } catch (uploadError) {
        // Fallback: simulate upload and store in local state
        setUploadProgress(prev => ({ ...prev, [documentType]: 100 }));

        const mockDocument: DocumentData = {
          document_id: `mock_${Date.now()}_${documentType}`,
          document_type: documentType,
          file_name: file.name,
          entity_type: 'application',
          entity_id: applicationId!,
          file_path: `mock_path/${file.name}`,
          file_size: file.size,
          mime_type: file.type,
          is_required: uploadData.is_required,
          created_at: new Date().toISOString()
        };

        // Update uploaded documents list with mock document
        setUploadedDocuments(prev => [...prev, mockDocument]);

        console.log(`✅ Document ${documentType} stored locally`);
      }

      // Remove from pending files
      setDocumentFiles(prev => {
        const newFiles = { ...prev };
        delete newFiles[documentType];
        return newFiles;
      });

      return true;

    } catch (error: any) {
      setValidationErrors(prev => ({
        ...prev,
        [documentType]: 'Failed to process document. Please try again.'
      }));
      setUploadProgress(prev => {
        const newProgress = { ...prev };
        delete newProgress[documentType];
        return newProgress;
      });
      return false;
    }
  };

  // Save all documents
  const handleSave = async (): Promise<boolean> => {
    if (!applicationId) {
      setValidationErrors({ save: 'Application ID is required' });
      return false;
    }

    setIsSaving(true);
    try {
      // Skip validation - allow users to upload documents gradually
      console.log('📋 Skipping document validation - users can upload documents gradually');

      // Clear any existing validation errors
      setValidationErrors({});

      console.log('💾 Processing documents for application:', applicationId);

      // Upload all pending documents
      if (Object.keys(documentFiles).length > 0) {
        const uploadPromises = Object.entries(documentFiles).map(([docType, file]) =>
          uploadDocument(docType, file)
        );

        const uploadResults = await Promise.all(uploadPromises);
        const allUploaded = uploadResults.every(result => result);

        if (!allUploaded) {
          throw new Error('Some documents failed to upload');
        }
      } else {
        console.log('📋 No pending documents to upload');
      }

      // Document status is tracked through the documents API

      // Update application progress
      try {
        await applicationService.updateApplication(applicationId, {
          current_step: 7,
          progress_percentage: 86 // Documents step completed (~7/8 steps)
        });
        console.log('📈 Application progress updated');
      } catch (progressError) {
        console.warn('Failed to update application progress:', progressError);
      }

      setValidationErrors({});
      console.log('✅ All documents processed successfully');
      return true;

    } catch (error: any) {
      console.error('❌ Error saving documents:', error);
      setValidationErrors({ save: 'Failed to process documents. Please try again.' });
      return false;
    } finally {
      setIsSaving(false);
    }
  };

  // Navigation functions using dynamic navigation
  const handleNext = async () => {
    await dynamicHandleNext(handleSave);
  };

  const handlePrevious = () => {
    dynamicHandlePrevious();
  };

  // Remove uploaded document
  const handleRemoveDocument = async (documentId: string) => {
    try {
      // Check if it's a mock document (starts with 'mock_')
      if (documentId.startsWith('mock_')) {
        // Just remove from local state
        setUploadedDocuments(prev => prev.filter(doc => doc.document_id !== documentId));
        console.log('🗑️ Mock document removed from local storage');
      } else {
        // Try to remove from backend
        try {
          await documentService.deleteDocument(documentId);
          setUploadedDocuments(prev => prev.filter(doc => doc.document_id !== documentId));
          console.log('🗑️ Document removed from backend successfully');
        } catch (backendError) {
          console.warn('⚠️ Backend removal failed, removing from local state:', backendError);
          setUploadedDocuments(prev => prev.filter(doc => doc.document_id !== documentId));
          console.log('🗑️ Document removed from local state');
        }
      }
    } catch (error) {
      console.error('❌ Error removing document:', error);
      setValidationErrors(prev => ({
        ...prev,
        remove: 'Failed to remove document. Please try again.'
      }));
    }
  };



  // Check if document is uploaded
  const isDocumentUploaded = (docType: string): boolean => {
    return uploadedDocuments.some(doc => doc.document_type === docType);
  };

  // Get uploaded document
  const getUploadedDocument = (docType: string): DocumentData | undefined => {
    return uploadedDocuments.find(doc => doc.document_type === docType);
  };

  // Create comprehensive document list combining required and uploaded documents
  const getComprehensiveDocumentList = () => {
    const documentMap = new Map();

    // Add all required documents first
    if (Array.isArray(requiredDocuments)) {
      requiredDocuments.forEach(reqDoc => {
        const docType = reqDoc.name.toLowerCase().replace(/\s+/g, '_');
        documentMap.set(docType, {
          type: 'required',
          requiredDoc: reqDoc,
          uploadedDoc: null,
          docType,
          isRequired: reqDoc.is_required,
          isUploaded: false
        });
      });
    }

    // Add/update with uploaded documents
    uploadedDocuments.forEach(uploadedDoc => {
      const docType = uploadedDoc.document_type;
      const existing = documentMap.get(docType);

      if (existing) {
        // Update existing required document with uploaded info
        documentMap.set(docType, {
          ...existing,
          uploadedDoc,
          isUploaded: true
        });
      } else {
        // Add uploaded document that's not in required list
        documentMap.set(docType, {
          type: 'uploaded',
          requiredDoc: null,
          uploadedDoc,
          docType,
          isRequired: false,
          isUploaded: true
        });
      }
    });

    return Array.from(documentMap.values());
  };

  const comprehensiveDocumentList = getComprehensiveDocumentList();

  // Handle document preview
  const handlePreviewDocument = async (doc: DocumentData) => {
    try {
      const blob = await documentService.previewDocument(doc.document_id!);
      const url = URL.createObjectURL(blob);
      window.open(url, '_blank');
    } catch (error) {
      console.error('Error previewing document:', error);
      alert('Failed to preview document');
    }
  };

  // Handle document download
  const handleDownloadDocument = async (doc: DocumentData) => {
    try {
      const blob = await documentService.downloadDocument(doc.document_id!);
      const url = URL.createObjectURL(blob);
      const link = window.document.createElement('a');
      link.href = url;
      link.download = doc.file_name;
      window.document.body.appendChild(link);
      link.click();
      link.remove();
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading document:', error);
      alert('Failed to download document');
    }
  };

  if (authLoading || isLoading) {
    return (
      <CustomerLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading required documents...</p>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  if (error) {
    return (
      <CustomerLayout>
        <div className="max-w-4xl mx-auto p-6">
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
            <div className="flex items-center">
              <i className="ri-error-warning-line text-red-600 dark:text-red-400 text-2xl mr-4"></i>
              <div>
                <h3 className="text-lg font-medium text-red-800 dark:text-red-200">Error Loading Documents</h3>
                <p className="text-red-700 dark:text-red-300 mt-1">{error}</p>
                <button
                  onClick={() => dynamicHandlePrevious()}
                  className="mt-4 inline-flex items-center px-4 py-2 border border-red-300 dark:border-red-600 rounded-md text-sm font-medium text-red-700 dark:text-red-300 bg-white dark:bg-red-900/20 hover:bg-red-50 dark:hover:bg-red-900/40"
                >
                  <i className="ri-arrow-left-line mr-2"></i>
                  Go Back
                </button>
              </div>
            </div>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  return (
    <CustomerLayout>
      <ApplicationLayout
        onNext={handleNext}
        onPrevious={handlePrevious}
        onSave={handleSave}
        showNextButton={true}
        showPreviousButton={true}
        showSaveButton={true}
        nextButtonText={nextStep ? `Continue to ${nextStep.name}` : "Continue"}
        previousButtonText="Back to Previous Step"
        saveButtonText="Save & Continue"
        nextButtonDisabled={false}
        isSaving={isSaving}
      >
        {/* Header */}
        <div className="mb-6">
          <h2 className="text-lg font-medium text-gray-900 dark:text-gray-100">
            Required Documents
          </h2>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Upload documents for your license application. You can upload documents gradually and return to this page later.
          </p>
          {applicationId && (
            <div className="mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <p className="text-sm text-blue-700 dark:text-blue-300">
                <i className="ri-file-upload-line mr-1"></i>
                Application ID: {applicationId}
              </p>
            </div>
          )}
          {loadingWarning && (
            <div className="mt-2 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
              <p className="text-sm text-yellow-700 dark:text-yellow-300">
                ⚠️ {loadingWarning}
              </p>
            </div>
          )}
        </div>

        {/* Validation Errors */}
        {Object.keys(validationErrors).length > 0 && (
          <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
            <h3 className="text-sm font-medium text-red-800 dark:text-red-200 mb-2">Please fix the following errors:</h3>
            <ul className="text-sm text-red-700 dark:text-red-300 list-disc list-inside">
              {Object.entries(validationErrors).map(([field, error]) => (
                <li key={field}>{error}</li>
              ))}
            </ul>
          </div>
        )}

        {/* Documents Upload Section */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6">
          <div className="space-y-6">
            <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                Document Upload
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                Upload your documents as they become available. Accepted formats: PDF, DOC, DOCX, JPG, PNG (Max 10MB each)
              </p>
            </div>

            {/* Always show the documents section */}
            <div>
              {/* Show required documents if any exist */}
              {Array.isArray(requiredDocuments) && requiredDocuments.length > 0 ? (
                <div className="space-y-4">
                  {comprehensiveDocumentList.map((docItem) => {
                    const { docType, requiredDoc, uploadedDoc, isRequired, isUploaded } = docItem;
                    const isUploading = uploadProgress[docType] !== undefined;

                  return (
                    <div key={requiredDoc.license_category_document_id} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                            {requiredDoc.name}
                            {requiredDoc.is_required && <span className="text-red-500 ml-1">*</span>}
                          </h4>
                          {isUploaded && uploadedDoc && (
                            <p className="text-xs text-green-600 dark:text-green-400 mt-1">
                              ✅ Uploaded: {uploadedDoc.file_name}
                            </p>
                          )}
                        </div>
                        {isUploaded && uploadedDoc && (
                          <div className="flex items-center space-x-2">
                            {documentService.isPreviewable(uploadedDoc.file_name) && (
                            <button
                              onClick={() => handlePreviewDocument(uploadedDoc)}
                              className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 text-sm"
                            >
                              <i className="ri-eye-line mr-1"></i>
                              Preview
                            </button>
                          )}
                          <button
                            onClick={() => handleDownloadDocument(uploadedDoc)}
                            className="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-200 text-sm"
                          >
                            <i className="ri-download-line mr-1"></i>
                            Download
                          </button>
                          <button
                            onClick={() => handleRemoveDocument(uploadedDoc.document_id!)}
                            className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200 text-sm"
                          >
                            <i className="ri-delete-bin-line mr-1"></i>
                            Remove
                          </button>
                          </div>
                        )}
                      </div>

                      {!isUploaded && (
                        <FileUpload
                          id={`document-${docType}`}
                          label=""
                          accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                          required={false}
                          maxSize={10}
                          value={documentFiles[docType] || null}
                          onChange={(file) => handleFileChange(docType, file)}
                          description={`Upload ${requiredDoc.name.toLowerCase()}`}
                        />


                      )}

                      {isUploading && (
                        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                          <div
                            className="bg-primary h-2 rounded-full transition-all duration-300"
                            style={{ width: `${uploadProgress[docType]}%` }}
                          ></div>
                        </div>
                      )}

                      {validationErrors[docType] && (
                        <p className="text-sm text-red-600 dark:text-red-400">
                          {validationErrors[docType]}
                        </p>
                      )}
                    </div>
                  );
                })}
              </div>
              ) : (
                /* No required documents - show general attachment upload */
                <div className="text-center py-8">
                  <i className="ri-attachment-line text-4xl text-gray-400 dark:text-gray-500 mb-4"></i>
                  <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                    No Required Documents
                  </h4>
                  <p className="text-gray-600 dark:text-gray-400 mb-6">
                    No specific documents are required for this license category.
                    You can upload any supporting documents or attachments below.
                  </p>

                  {/* General attachment upload */}
                  <div className="max-w-md mx-auto">
                    <FileUpload
                      id="general-attachment"
                      label="Upload Supporting Documents"
                      accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.txt"
                      onChange={(file) => {
                        if (file) {
                          setDocumentFiles(prev => ({ ...prev, 'general_attachment': file }));
                        }
                      }}
                      className="mb-4"
                    />
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Supported formats: PDF, DOC, DOCX, JPG, PNG, TXT (Max 10MB)
                    </p>
                  </div>
                </div>
              )}

              {/* Show uploaded documents if any exist */}
              {/* {uploadedDocuments.length > 0 && (
                <div className="mt-8">
                  <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
                    Uploaded Documents ({uploadedDocuments.length})
                  </h4>
                  <div className="space-y-3">
                    {uploadedDocuments.map((doc) => (
                      <div key={doc.document_id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div className="flex items-center space-x-3">
                          <i className="ri-file-line text-gray-400"></i>
                          <div>
                            <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                              {doc.document_type}
                            </p>
                            <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                              {doc.file_name}
                            </p>
                            <p className="text-xs text-gray-500 dark:text-gray-400">
                              {doc.document_type} • Uploaded {doc.created_at ? new Date(doc.created_at).toLocaleDateString() : 'Unknown date'}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          {documentService.isPreviewable(doc.file_name) && (
                            <button
                              onClick={() => handlePreviewDocument(doc)}
                              className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 text-sm"
                            >
                              <i className="ri-eye-line mr-1"></i>
                              Preview
                            </button>
                          )}
                          <button
                            onClick={() => handleDownloadDocument(doc)}
                            className="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-200 text-sm"
                          >
                            <i className="ri-download-line mr-1"></i>
                            Download
                          </button>
                          <button
                            onClick={() => handleRemoveDocument(doc.document_id!)}
                            className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200 text-sm"
                          >
                            <i className="ri-delete-bin-line mr-1"></i>
                            Remove
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )} */}
            </div>
          </div>
        </div>

        {/* Upload Summary */}
        {Array.isArray(requiredDocuments) && requiredDocuments.length > 0 && (
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
              Upload Summary
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <span className="text-gray-600 dark:text-gray-400">Total Required:</span>
                <span className="ml-2 font-medium text-gray-900 dark:text-gray-100">
                  {requiredDocuments.filter(doc => doc.is_required).length}
                </span>
              </div>
              <div>
                <span className="text-gray-600 dark:text-gray-400">Uploaded:</span>
                <span className="ml-2 font-medium text-green-600 dark:text-green-400">
                  {uploadedDocuments.length}
                </span>
              </div>
              <div>
                <span className="text-gray-600 dark:text-gray-400">Pending:</span>
                <span className="ml-2 font-medium text-orange-600 dark:text-orange-400">
                  {Object.keys(documentFiles).length}
                </span>
              </div>
            </div>
          </div>
        )}

      </ApplicationLayout>
    </CustomerLayout>
  );
};

export default DocumentsPage;
