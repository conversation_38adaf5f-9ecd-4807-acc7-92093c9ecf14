'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import CustomerLayout from '@/components/customer/CustomerLayout';
import { useAuth } from '@/contexts/AuthContext';
import { applicationService } from '@/services/applicationService';

const SubmittedPage: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { isAuthenticated, loading: authLoading } = useAuth();

  // URL parameters
  const applicationId = searchParams.get('application_id');

  // State management
  const [isLoading, setIsLoading] = useState(true);
  const [application, setApplication] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  // Load application data
  useEffect(() => {
    const loadData = async () => {
      if (!applicationId || !isAuthenticated || authLoading) return;

      try {
        setIsLoading(true);
        setError(null);

        console.log('🔄 Loading submitted application:', applicationId);

        // Load application data
        const app = await applicationService.getApplication(applicationId);
        setApplication(app);

        console.log('✅ Application data loaded');

      } catch (err: any) {
        console.error('❌ Error loading application data:', err);
        setError('Failed to load application data');
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [applicationId, isAuthenticated, authLoading]);

  if (authLoading || isLoading) {
    return (
      <CustomerLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading submission details...</p>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  if (error) {
    return (
      <CustomerLayout>
        <div className="max-w-4xl mx-auto p-6">
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
            <div className="flex items-center">
              <i className="ri-error-warning-line text-red-600 dark:text-red-400 text-2xl mr-4"></i>
              <div>
                <h3 className="text-lg font-medium text-red-800 dark:text-red-200">Error Loading Application</h3>
                <p className="text-red-700 dark:text-red-300 mt-1">{error}</p>
                <button
                  onClick={() => router.push('/customer/applications')}
                  className="mt-4 inline-flex items-center px-4 py-2 border border-red-300 dark:border-red-600 rounded-md text-sm font-medium text-red-700 dark:text-red-300 bg-white dark:bg-red-900/20 hover:bg-red-50 dark:hover:bg-red-900/40"
                >
                  <i className="ri-arrow-left-line mr-2"></i>
                  Go to Applications
                </button>
              </div>
            </div>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  return (
    <CustomerLayout>
      <div className="max-w-4xl mx-auto p-6">
        {/* Success Header */}
        <div className="text-center mb-8">
          <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 dark:bg-green-900/20 mb-4">
            <i className="ri-check-line text-green-600 dark:text-green-400 text-2xl"></i>
          </div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
            Application Submitted Successfully!
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Your license application has been submitted to MACRA for review.
          </p>
        </div>

        {/* Application Details */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6">
          <h2 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
            Application Details
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-gray-600 dark:text-gray-400">Application ID</label>
              <p className="text-sm text-gray-900 dark:text-gray-100 font-mono">{applicationId}</p>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-600 dark:text-gray-400">Status</label>
              <p className="text-sm text-green-600 dark:text-green-400 font-medium">
                <i className="ri-check-circle-line mr-1"></i>
                Submitted
              </p>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-600 dark:text-gray-400">Submitted Date</label>
              <p className="text-sm text-gray-900 dark:text-gray-100">
                {application?.submitted_at ? new Date(application.submitted_at).toLocaleDateString() : 'Today'}
              </p>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-600 dark:text-gray-400">Progress</label>
              <p className="text-sm text-gray-900 dark:text-gray-100">100% Complete</p>
            </div>
          </div>
        </div>

        {/* Next Steps */}
        <div className="bg-blue-50 dark:bg-blue-900/20 p-6 rounded-lg mb-6">
          <h3 className="text-lg font-medium text-blue-900 dark:text-blue-100 mb-4">
            <i className="ri-information-line mr-2"></i>
            What Happens Next?
          </h3>
          
          <div className="space-y-3">
            <div className="flex items-start">
              <div className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-medium mr-3 mt-0.5">
                1
              </div>
              <div>
                <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100">Application Review</h4>
                <p className="text-sm text-blue-800 dark:text-blue-200">
                  MACRA will review your application within 30 business days.
                </p>
              </div>
            </div>
            
            <div className="flex items-start">
              <div className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-medium mr-3 mt-0.5">
                2
              </div>
              <div>
                <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100">Email Notifications</h4>
                <p className="text-sm text-blue-800 dark:text-blue-200">
                  You'll receive email updates about your application status.
                </p>
              </div>
            </div>
            
            <div className="flex items-start">
              <div className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-medium mr-3 mt-0.5">
                3
              </div>
              <div>
                <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100">Additional Documentation</h4>
                <p className="text-sm text-blue-800 dark:text-blue-200">
                  We may request additional documents during the review process.
                </p>
              </div>
            </div>
            
            <div className="flex items-start">
              <div className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-medium mr-3 mt-0.5">
                4
              </div>
              <div>
                <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100">Final Decision</h4>
                <p className="text-sm text-blue-800 dark:text-blue-200">
                  You'll be notified of the final decision on your license application.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Important Notes */}
        <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg mb-6">
          <h4 className="text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-2">
            <i className="ri-alert-line mr-2"></i>
            Important Notes
          </h4>
          <ul className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
            <li>• Keep your application ID for reference: <strong>{applicationId}</strong></li>
            <li>• Check your email regularly for updates</li>
            <li>• You can track your application status in your dashboard</li>
            <li>• Contact MACRA support if you have any questions</li>
          </ul>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <button
            onClick={() => router.push('/customer/my-licenses')}
            className="inline-flex items-center justify-center px-6 py-3 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            <i className="ri-file-list-line mr-2"></i>
            View All Applications
          </button>
          
          <button
            onClick={() => router.push('/customer')}
            className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            <i className="ri-dashboard-line mr-2"></i>
            Go to Dashboard
          </button>
        </div>

        {/* Support Contact */}
        <div className="text-center mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Need help? Contact MACRA support at{' '}
            <a href="mailto:<EMAIL>" className="text-primary hover:text-primary-dark">
              <EMAIL>
            </a>
            {' '}or call{' '}
            <a href="tel:+265123456789" className="text-primary hover:text-primary-dark">
              +265 123 456 789
            </a>
          </p>
        </div>
      </div>
    </CustomerLayout>
  );
};

export default SubmittedPage;
