/* Import auth animations */
@import '../styles/auth-animations.css';

/* stylelint-disable at-rule-no-unknown */
/* CSS validation disabled for Tailwind CSS directives */
/* This file uses PostCSS with Tailwind CSS */
@tailwind base;
@tailwind components;
@tailwind utilities;
/* stylelint-enable at-rule-no-unknown */

/* Performance optimizations */
@layer base {
  /* Optimize page transitions */
  .page-transitioning {
    pointer-events: none;
  }
  
  .page-transitioning * {
    transition: none !important;
    animation: none !important;
  }
  
  /* Optimize images */
  img {
    content-visibility: auto;
  }
  
  /* Optimize animations for reduced motion */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
  
  /* GPU acceleration for smooth animations */
  .gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
  }
}

@layer components {
  /* Dashboard card optimizations */
  .dashboard-card {
    contain: layout style paint;
    will-change: transform;
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  }
  
  .dashboard-card:hover {
    transform: translateY(-2px) translateZ(0);
  }
  
  /* Navigation item optimizations */
  .nav-item {
    will-change: background-color, color;
    transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out;
  }
  
  .nav-item:hover {
    transform: translateZ(0);
  }
  
  /* Button optimizations */
  .btn-primary {
    will-change: background-color, transform;
    transition: all 0.2s ease-in-out;
  }
  
  .btn-primary:hover {
    transform: translateZ(0);
  }
  
  /* Loading skeleton */
  .loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
  }
  
  @keyframes loading {
    0% {
      background-position: 200% 0;
    }
    100% {
      background-position: -200% 0;
    }
  }
}

/* Template Form Styles */
/* stylelint-disable at-rule-no-unknown */
@layer components {
  .custom-form-label {
    @apply block text-sm font-medium text-gray-700 pb-2;
  }

  .enhanced-input {
    @apply appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm;
  }

  .enhanced-select {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm;
  }

  .enhanced-checkbox {
    @apply h-5 w-5 text-primary focus:ring-primary border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700;
  }

  .main-button {
    @apply inline-flex py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-red-700 focus:ring focus:ring-red-200;
  }

  .secondary-main-button {
    @apply inline-flex py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring focus:ring-gray-200;
  }

  .custom-input {
    @apply mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 text-sm focus:shadow-md focus:shadow-red-200 focus:border-red-500 focus:outline-none;
  }

  .form-section {
    @apply flex-col flex gap-y-2 lg:border-b divide-solid border-gray-300;
  }

  .inner-form-section {
    @apply mt-4 grid gap-y-6 gap-x-6 sm:grid-cols-2 lg:grid-cols-2;
  }

  .tab-heading {
    @apply flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0 mb-6;
  }

  .form-group {
    @apply mb-6;
  }
}
/* stylelint-enable at-rule-no-unknown */

:root {
  --background: #ffffff;
  --foreground: #171717;
  --primary: #e02b20;
  --secondary:#ce4721;
}


@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
    --primary: #1D4ED8;
  }
}

html, body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Inter', sans-serif;
  background-color: #f9fafb;
  height: 100%;
}

/* Custom scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #dc2626;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #b91c1c;
}

/* Firefox scrollbar */
html {
  scrollbar-width: thin;
  scrollbar-color: #dc2626 #f1f1f1;
}

.bg-primary {
  background-color: var(--primary);
}

.hover\:bg-primary:hover {
  background-color: var(--primary);
}
.rounded-button {
  border-radius: 0.375rem; /* Adjust as needed */
}

.logo {
  width: 100px;
  height: auto; /* Keeps aspect ratio */
}

.sidebar-logo {
  height: 2.5rem; /* 40px */
  width: auto;
  max-width: 120px;
  object-fit: contain;
}

input[type="checkbox"] {
  appearance: none;
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid #d1d5db;
  border-radius: 4px;
  position: relative;
  cursor: pointer;
  background-color: #ffffff;
}

.dark input[type="checkbox"] {
  border-color: #4b5563;
  background-color: #374151;
}

input[type="checkbox"]:checked {
  background-color: #e02b20;
  border-color: #e02b20;
}

input[type="checkbox"]:checked::after {
  content: "";
  position: absolute;
  left: 6px;
  top: 2px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.custom-switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
}

.custom-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.switch-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #e5e7eb;
  transition: .4s;
  border-radius: 34px;
}

.switch-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .switch-slider {
  background-color: #4f46e5;
}

input:checked + .switch-slider:before {
  transform: translateX(20px);
}

.custom-range {
  -webkit-appearance: none;
  appearance: none;
  width: 100%;
  height: 6px;
  background: #e5e7eb;
  border-radius: 5px;
  outline: none;
}

.custom-range::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #4f46e5;
  cursor: pointer;
}

.custom-range::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #4f46e5;
  cursor: pointer;
  border: none;
}

.custom-radio {
  appearance: none;
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid #d1d5db;
  border-radius: 50%;
  position: relative;
  cursor: pointer;
}

.custom-radio:checked {
  border-color: #4f46e5;
}

.custom-radio:checked::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 0.625rem;
  height: 0.625rem;
  border-radius: 50%;
  background-color: #4f46e5;
}

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  appearance: none;
  margin: 0;
}

.tab-button {
  position: relative;
  z-index: 1;
}

.tab-button.active {
  color: #e02b20;
  font-weight: 500;
}

.tab-button.active::after {
  content: '';
  position: absolute;
  bottom: -6px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #e02b20;
}

.tab-content {
  display: block;
}

.tab-content.hidden {
  display: none;
}

.license-card {
  transition: transform 0.3s ease;
}

.license-card:hover {
  transform: translateY(-4px);
}

.dropdown-content {
  display: none;
  position: absolute;
  right: 0;
  top: 100%;
  z-index: 50;
}

.dropdown-content.show {
  display: block;
}

/* Enhanced form styles */
.enhanced-input {
  appearance: none;
  display: block;
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid #d1d5db;
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  background-color: #f9fafb;
  font-size: 0.875rem;
  line-height: 1.25rem;
  transition: all 0.15s ease-in-out;
}

.enhanced-input:hover {
  background-color: #ffffff;
}

.enhanced-input:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--primary);
  border-color: var(--primary);
}

.enhanced-input::placeholder {
  color: #9ca3af;
}

.enhanced-select {
  display: block;
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid #d1d5db;
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  background-color: #f9fafb;
  font-size: 0.875rem;
  line-height: 1.25rem;
  transition: all 0.15s ease-in-out;
}

.enhanced-select:hover {
  background-color: #ffffff;
}

.enhanced-select:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--primary);
  border-color: var(--primary);
}

.enhanced-checkbox {
  height: 1.25rem;
  width: 1.25rem;
  color: var(--primary);
  border: 2px solid #d1d5db;
  border-radius: 0.25rem;
  background-color: #ffffff;
}

.dark .enhanced-checkbox {
  border-color: #4b5563;
  background-color: #374151;
}

.enhanced-checkbox:focus {
  box-shadow: 0 0 0 2px var(--primary);
}

.enhanced-button {
  display: flex;
  justify-content: center;
  padding: 0.75rem 1rem;
  border: 2px solid transparent;
  border-radius: 0.375rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  color: #ffffff;
  background-color: var(--primary);
  transition: all 0.15s ease-in-out;
}

.enhanced-button:hover {
  opacity: 0.9;
}

.enhanced-button:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--primary), 0 0 0 4px rgba(224, 43, 32, 0.2);
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

.side-nav {
  overflow: auto;
  -ms-overflow-style: none;
  height: 75vh;
}

.side-nav::-webkit-scrollbar {
  display: none;
}

.side-nav {
  scrollbar-width: none;
}

/* Mobile sidebar styles */
@media (max-width: 768px) {
  .mobile-sidebar-open {
    display: block !important;
    position: fixed;
    z-index: 50;
    height: 100vh;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
  }

  .mobile-sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 40;
    display: none;
  }

  .mobile-sidebar-overlay.show {
    display: block;
  }
}

/* Template-specific styles for user management */
/* stylelint-disable at-rule-no-unknown */
.btn-active-primary {
  @apply inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 hover:text-white bg-white border border-primary rounded-full hover:bg-primary transition transform hover:translate-x-1;
}

.btn-active-primary::after {
  content: '_↗';
}

.btn-active-delete {
  @apply inline-flex items-center px-4 py-2 text-xs font-medium text-gray-300 hover:text-gray-300 bg-white border border-gray-200 rounded-full transition transform hover:translate-x-1;
}

.btn-active-delete::after {
  content: '_↗';
}

.table-responsive {
  @apply min-w-full divide-y divide-gray-200 md:border-separate table-auto;
}
/* stylelint-enable at-rule-no-unknown */