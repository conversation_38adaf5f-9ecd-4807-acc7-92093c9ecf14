'use client';

import { useState } from 'react';
import { useToast } from '../../contexts/ToastContext';
import { Task } from '@/services/task-assignment';
import TasksTab from '../../components/tasks/TasksTab';
import TaskModal from '../../components/tasks/TaskModal';

export default function TasksPage() {
  const { showSuccess } = useToast();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingTask, setEditingTask] = useState<Task | null>(null);

  const handleEditTask = (task: Task) => {
    setEditingTask(task);
    setIsModalOpen(true);
  };

  const handleCreateTask = () => {
    setEditingTask(null);
    setIsModalOpen(true);
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    setEditingTask(null);
  };

  const handleTaskSaved = () => {
    if (editingTask) {
      showSuccess('Task updated successfully!');
    } else {
      showSuccess('Task created successfully!');
    }
    // The TasksTab component will automatically refresh its data
  };

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
          Task Management
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Manage and track tasks across your organization
        </p>
      </div>

      {/* Task Content */}
      <TasksTab
        onEditTask={handleEditTask}
        onCreateTask={handleCreateTask}
      />
      {/* Task Modal */}
      <TaskModal
        isOpen={isModalOpen}
        onClose={handleModalClose}
        onSave={handleTaskSaved}
        task={editingTask}
      />
    </div>
  );
}