'use client';

import React, { useState, useEffect, use } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import EvaluationLayout from '@/components/evaluation/EvaluationLayout';
import { EvaluationForm } from '@/components/evaluation';
import { useDynamicNavigation } from '@/hooks/useDynamicNavigation';
import { applicationService } from '@/services/applicationService';
import { documentService } from '@/services/documentService';

interface EvaluateDocumentsPageProps {
  params: Promise<{
    'license-type': string;
  }>;
}

const EvaluateDocumentsPage: React.FC<EvaluateDocumentsPageProps> = ({ params }) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { isAuthenticated, loading: authLoading, user } = useAuth();

  // Unwrap params using React.use()
  const resolvedParams = use(params);
  const licenseType = resolvedParams['license-type'];
  const applicationId = searchParams.get('application_id');

  // State
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [application, setApplication] = useState<any>(null);
  const [documentsData, setDocumentsData] = useState<any[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [licenseCategoryId, setLicenseCategoryId] = useState<string | null>(null);

  // Dynamic navigation hook - same as apply pages
  const {
    nextStep,
    previousStep,
  } = useDynamicNavigation({
    currentStepRoute: 'documents',
    licenseCategoryId,
    applicationId
  });

  // Load application and documents data
  useEffect(() => {
    const loadData = async () => {
      if (!applicationId || !isAuthenticated) return;

      try {
        setLoading(true);
        setError(null);

        // Load application details
        const appResponse = await applicationService.getApplication(applicationId);
        setApplication(appResponse);

        // Set license category ID for navigation
        if (appResponse?.license_category_id) {
          setLicenseCategoryId(appResponse.license_category_id);
        }

        // Load documents data if available
        if (applicationId) {
          try {
            const documentsResponse = await documentService.getDocumentsByEntity('application', applicationId);
            setDocumentsData(documentsResponse || []);
          } catch (err: any) {
            console.warn('Documents data not available:', err?.response?.status || 'Unknown error');
            // Continue without documents data - this is expected for applications without documents
            setDocumentsData([]);
          }
        }
      } catch (err: any) {
        console.error('Error loading application data:', err);
        setError('Failed to load application data');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [applicationId, isAuthenticated]);

  // Navigation handlers - modified for evaluation
  const handleNext = () => {
    if (!applicationId || !nextStep) return;

    const params = new URLSearchParams();
    params.set('application_id', applicationId);
    if (licenseCategoryId) {
      params.set('license_category_id', licenseCategoryId);
    }
    router.push(`/applications/${licenseType}/evaluate/${nextStep.route}?${params.toString()}`);
  };

  const handlePrevious = () => {
    if (!applicationId || !previousStep) return;

    const params = new URLSearchParams();
    params.set('application_id', applicationId);
    if (licenseCategoryId) {
      params.set('license_category_id', licenseCategoryId);
    }
    router.push(`/applications/${licenseType}/evaluate/${previousStep.route}?${params.toString()}`);
  };

  // Evaluation handlers
  const handleStatusUpdate = async (status: string, comment: string) => {
    if (!applicationId) return;

    try {
      setIsSubmitting(true);
      console.log('Status update:', { applicationId, status, comment });
      console.log('Status updated successfully');
    } catch (err) {
      console.error('Error updating status:', err);
      setError('Failed to update application status');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCommentSave = async (comment: string) => {
    if (!applicationId) return;

    try {
      setIsSubmitting(true);
      console.log('Comment save:', { applicationId, step: 'documents', comment });
      console.log('Comment saved successfully');
    } catch (err) {
      console.error('Error saving comment:', err);
      setError('Failed to save comment');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleAttachmentUpload = async (file: File) => {
    if (!applicationId) return;

    try {
      setIsSubmitting(true);
      console.log('Attachment upload:', { applicationId, step: 'documents', fileName: file.name });
      console.log('Attachment uploaded successfully');
    } catch (err) {
      console.error('Error uploading attachment:', err);
      setError('Failed to upload attachment');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDocumentDownload = async (documentId: string, fileName: string) => {
    try {
      const blob = await documentService.downloadDocument(documentId);

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (err) {
      console.error('Error downloading document:', err);
      setError('Failed to download document');
    }
  };

  const getFileIcon = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return 'ri-file-pdf-line text-red-500';
      case 'doc':
      case 'docx':
        return 'ri-file-word-line text-blue-500';
      case 'xls':
      case 'xlsx':
        return 'ri-file-excel-line text-green-500';
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return 'ri-image-line text-purple-500';
      default:
        return 'ri-file-line text-gray-500';
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Loading state
  if (authLoading || loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading application data...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <i className="ri-error-warning-line text-4xl text-red-500 mb-4"></i>
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Error Loading Application</h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
          <button
            onClick={() => router.push('/applications')}
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
          >
            Back to Applications
          </button>
        </div>
      </div>
    );
  }

  // No application found
  if (!application) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <i className="ri-file-search-line text-4xl text-gray-400 mb-4"></i>
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Application Not Found</h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">The requested application could not be found.</p>
          <button
            onClick={() => router.push('/applications')}
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
          >
            Back to Applications
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 min-h-screen overflow-y-auto">
      <EvaluationLayout
        applicationId={applicationId!}
        licenseTypeCode={licenseType}
        currentStepRoute="documents"
        onNext={handleNext}
        onPrevious={handlePrevious}
        showNextButton={!!nextStep}
        showPreviousButton={!!previousStep}
        nextButtonDisabled={isSubmitting}
        previousButtonDisabled={isSubmitting}
        nextButtonText={nextStep ? `Continue to ${nextStep.name}` : "Continue"}
        previousButtonText={previousStep ? `Back to ${previousStep.name}` : "Back"}
      >
      {/* Documents Information Display */}
      <div className="space-y-6">
        {documentsData && documentsData.length > 0 ? (
          <>
            <div className="mb-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                Uploaded Documents ({documentsData.length})
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Supporting documents submitted with this application
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {documentsData.map((document, index) => (
                <div key={index} className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow">
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0">
                      <i className={`${getFileIcon(document.file_name)} text-2xl`}></i>
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                        {document.document_type || 'Document'}
                      </h4>
                      <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                        {document.file_name}
                      </p>
                      <div className="mt-2 flex items-center justify-between">
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          {document.file_size ? formatFileSize(document.file_size) : 'Unknown size'}
                        </span>
                        <button
                          onClick={() => handleDocumentDownload(document.document_id, document.file_name)}
                          className="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 flex items-center"
                        >
                          <i className="ri-download-line mr-1"></i>
                          Download
                        </button>
                      </div>
                    </div>
                  </div>

                  {document.description && (
                    <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
                      <p className="text-xs text-gray-600 dark:text-gray-400">
                        {document.description}
                      </p>
                    </div>
                  )}

                  <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
                    <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                      <span>
                        Uploaded: {document.created_at ? new Date(document.created_at).toLocaleDateString() : 'Unknown'}
                      </span>
                      <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                        document.is_verified ? 
                          'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 
                          'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                      }`}>
                        {document.is_verified ? 'Verified' : 'Pending'}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </>
        ) : (
          <div className="text-center py-8">
            <i className="ri-file-list-line text-4xl text-gray-400 mb-4"></i>
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">No Documents</h3>
            <p className="text-gray-600 dark:text-gray-400">
              No documents have been uploaded for this application.
            </p>
          </div>
        )}

      </div>

        {/* Evaluation Form */}
        <EvaluationForm
          applicationId={applicationId!}
          currentStep="documents"
          onStatusUpdate={handleStatusUpdate}
          onCommentSave={handleCommentSave}
          onAttachmentUpload={handleAttachmentUpload}
          isSubmitting={isSubmitting}
        />
      </EvaluationLayout>
    </div>
  );
};

export default EvaluateDocumentsPage;
